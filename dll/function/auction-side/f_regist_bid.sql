CREATE OR REPLACE FUNCTION public.f_regist_bid
(
    in_bid_user_no bigint,
    in_user_name character varying,
    in_bid_items json
)
RETURNS json
LANGUAGE plpgsql

AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 入札をN件登録する（開催回を複数またがっていても良い）
-- Parameters
-- @param in_bid_user_no 利用者番号
-- @param in_bid_items 入札内容JSON（exhibitionItemNo:出品番号、bidPrice:入札金額※0の場合入札取消、bidQuantity：数量（固定：1））
----------------------------------------------------------------------------------------------------

  -- レコード情報
  user_rec record; -- 利用者情報

  regist_bid_rec record; -- 入札情報(今回登録分)
  exhibition_rec record; -- 開催回情報
  after_top_data_rec record; -- 価格情報(入札後)

  -- 一次格納用
  temp_sql text = '';
  temp_sql_where character varying = '';
  temp_text text = '';
  temp_exhibition_item_no character varying;
  temp_price character varying;
  temp_json json;

  -- 開催回種別情報
  ext_auction_classification integer;
  ext_open_classification integer;
  ext_bid_count_limit integer;
  ext_bid_limit integer;
  ext_bid_min_useFlag integer;
  ext_bid_short_flag integer;
  ext_bid_commit_min_use_flag integer;
  ext_bid_commit_min_display_flag integer;
  ext_bid_cancel_flag integer;
  ext_deal_flag integer;
  ext_bid_commit_classification integer;
  ext_bidder_commit_classification integer;
  ext_bid_short_display_flag integer;
  ext_bid_count_display_flag integer;
  ext_price_display_flag integer;
  ext_risk_adjustment_flag integer;
  ext_extend_flag integer;
  ext_top_reduction_flag integer;

  -- 入札
  last_bid_no bigint;
  last_bid_price numeric;
  bid_count integer = 0;

  -- 入札取消
  after_bid_cancel_current_price numeric;

  -- 出品/開催回
  exhibition_member_no bigint;
  exhibition_item_no bigint;
  exhibition_id bigint;
  current_datetime timestamp without time zone;
  start_datetime timestamp without time zone;
  end_datetime timestamp without time zone;

  -- 価格
  price numeric;
  tax numeric;
  lowest_bid_accept_price numeric;

  -- 判定用
  error_flag boolean;
  top_or_current_price_changed_flag boolean;

  -- エラー
  error_count integer = 0;
  error_code character varying = '';
  error_message character varying = '';
  system_error_message character varying = '';

  -- 返却用
  tmp_top_text text = '';
  top_changed_text text = '';
  top_2nd_text text = '';
  top_changed_count integer = 0;
  result_data json;
  result_text text = '';
  status integer = 200;

  -- 入札した後の終了時間
  after_bid_end_datetime timestamp without time zone;

  -- Auto pitch
  auto_pitch_data_sql text = '';
  auto_pitch_sql text = '';
  input_price_pitch_width numeric;
  input_price_pitch_low numeric;
  current_price_pitch_width numeric;
  top_price_pitch_width numeric;
  after_top_top_price_pitch_width numeric;

  -- 2nd member no
  second_bidder_member_no bigint;
  second_price numeric;
  second_price_pitch_width numeric;
  temp_quantity integer;
  BEGIN

  -- 利用者情報
  temp_sql := 'SELECT MM.member_no,
                      MM.free_field->>''nickname'' AS nickname,
                      MM.currency_id,
                      MU.bid_allow_flag AS user_bid_allow_flag,
                      MM.bid_allow_flag AS member_bid_allow_flag,
                      MU.tenant_no
            FROM m_user MU
            LEFT JOIN m_member MM
              ON MM.member_no = MU.member_no
           WHERE MU.user_no = $1
             AND MU.delete_flag = 0
             AND MM.delete_flag = 0';
  EXECUTE temp_sql INTO user_rec USING in_bid_user_no;

  -- 利用者の入札可否フラグ
  IF user_rec.user_bid_allow_flag = 0 OR user_rec.member_bid_allow_flag = 0 THEN
    error_code := 'MEMBER_NOT_ALLOW_BIDDING_ERROR';
  END IF;

  IF error_code <> '' THEN
    RAISE EXCEPTION '%', error_code;
  END IF;

  ----------------------------------------------------------------------------------------------------
  -- 今回対象の商品（N件）の出品テーブルにLOCKをかける
  ----------------------------------------------------------------------------------------------------
  in_bid_items := to_json(in_bid_items);
  FOR temp_json IN SELECT * FROM json_array_elements(in_bid_items)
  LOOP

    IF temp_json ->> 'exhibitionItemNo' IS NOT NULL AND temp_json ->> 'exhibitionItemNo' <> '' THEN
      temp_exhibition_item_no := replace(temp_json ->> 'exhibitionItemNo', '"', '');
      IF bid_count <> 0 THEN
        temp_sql_where := temp_sql_where || ' OR ';
      END IF;
      IF NOT temp_exhibition_item_no :: text ~ '^([1-9]\d*|0)$' THEN
        temp_exhibition_item_no := '0';
      END IF;

      temp_sql_where := temp_sql_where || 'exhibition_item_no = ' || temp_exhibition_item_no;
      bid_count := bid_count + 1;
    END IF;

  END LOOP;

  IF temp_sql_where <> '' THEN
    temp_sql := 'SELECT 1
                   FROM t_exhibition_item
                  WHERE ' || temp_sql_where || '
                 FOR UPDATE';
    EXECUTE temp_sql;
  END IF;

  ----------------------------------------------------------------------------------------------------
  -- 入札対象データごとにループ処理
  ----------------------------------------------------------------------------------------------------
  FOR temp_json IN SELECT * FROM json_array_elements(in_bid_items)
  LOOP

    error_code := '';
    top_or_current_price_changed_flag := false;

    -- 現在時刻
    SELECT CURRENT_TIMESTAMP
      INTO current_datetime;

    -- JSON形式で取得する
    temp_exhibition_item_no := temp_json ->> 'exhibitionItemNo';
    temp_price := temp_json ->> 'bidPrice';

    -- 数量は1で固定する
    temp_quantity := 1;

    IF temp_exhibition_item_no IS NULL OR temp_exhibition_item_no = '' THEN
      error_code := 'EXHIBITION_ITEM_REQUIRED_ERROR';

    ELSEIF NOT temp_exhibition_item_no :: text ~ '^([1-9]\d*|0)$' THEN
      error_code := 'EXHIBITION_ITEM_FORMAT_ERROR';

    ELSEIF temp_price IS NULL OR temp_price = '' THEN
      error_code := 'PRICE_REQUIRED_ERROR';

    ELSEIF NOT temp_price :: text ~ '^([0-9]\d{0,9})(\.\d{0,2})?$' THEN
      error_code := 'PRICE_FORMAT_ERROR';

    ELSE
      error_flag := false;
      exhibition_item_no := replace(temp_exhibition_item_no, '"', '');
      price := replace(temp_price, '"', '');

      ----------------------------------------------------------------------------------------------------
      -- 当該商品の開催回および出品の情報を取得する
      ----------------------------------------------------------------------------------------------------
      temp_sql := 'SELECT B.tenant_no
                        , C.category_id
                        , B.exhibition_item_no
                        , (CASE
                            WHEN B.end_datetime > COALESCE(B.default_end_datetime, B.end_datetime) THEN B.end_datetime
                            ELSE COALESCE(B.default_end_datetime, B.end_datetime)
                          END) AS end_datetime
                        , B.default_end_datetime
                        , C.pitch_option
                        , CASE WHEN C.pitch_option = 1 THEN COALESCE(C.pitch_width, 0) ELSE COALESCE(NULLIF(AUTO_PITCH.value1, '''')::numeric, 0) END AS pitch_width
                        , COALESCE(B.lowest_bid_price, 0) AS lowest_bid_price
                        , COALESCE(B.lowest_bid_accept_price, 0) AS lowest_bid_accept_price
                        , COALESCE(B.deal_bid_price, 0) AS deal_bid_price
                        , COALESCE(B.top_member_no, -1) AS top_member_no
                        , TBH.bid_user_name AS top_bid_user_name
                        , COALESCE(B.top_price, 0) AS top_price
                        , COALESCE(B.current_price, 0) AS current_price
                        , B.exhibition_member_no
                        , B.hummer_flag
                        , B.cancel_flag
                        , B.delete_flag exhibition_item_delete_flag
                        , C.exhibition_no
                        , C.start_datetime
                        , C.max_extend_datetime
                        , C.extend_judge_minutes
                        , C.extend_minutes
                        , C.currency_id
                        , C.exhibition_classification_info
                        , C.more_little_judge_pitch
                     FROM t_exhibition_item B
                          LEFT OUTER JOIN t_exhibition C
                                       ON B.exhibition_no = C.exhibition_no
                                      AND C.delete_flag = 0
                          LEFT JOIN t_bid_history TBH
                                 ON TBH.exhibition_item_no = B.exhibition_item_no
                                AND TBH.member_no = B.top_member_no
                                AND TBH.bid_price = B.top_price
                          LEFT JOIN (
                            SELECT MAX(value1) AS value1, MAX(value2) AS value2, value3, value4
                              FROM m_constant_localized
                            WHERE constant_no IN (SELECT constant_no FROM m_constant WHERE key_string = $2)
                              AND language_code = $3
                            GROUP BY value3, value4
                          ) AUTO_PITCH ON (AUTO_PITCH.value3 IS NULL OR LENGTH(AUTO_PITCH.value3) = 0 OR COALESCE(B.current_price, COALESCE(B.lowest_bid_price, 0)) >= COALESCE(NULLIF(AUTO_PITCH.value3, '''')::numeric, 0))
                                      AND (AUTO_PITCH.value4 IS NULL OR LENGTH(AUTO_PITCH.value4) = 0 OR COALESCE(B.current_price, COALESCE(B.lowest_bid_price, 0)) < COALESCE(NULLIF(AUTO_PITCH.value4, '''')::numeric, 0))
                    WHERE B.exhibition_item_no = $1
                    ORDER BY TBH.create_datetime DESC
                    LIMIT 1';
      EXECUTE temp_sql INTO exhibition_rec USING exhibition_item_no, 'PITCH_FOLLOW_BID_PRICE', 'ja';

      -- Get pitch width by input price
      auto_pitch_data_sql := 'SELECT COALESCE(NULLIF(AUTO_PITCH.value3, '''')::numeric, 0) AS value3,
                                COALESCE(NULLIF(AUTO_PITCH.value1, '''')::numeric, 0) AS pitch_width
                    FROM (
                      SELECT MAX(value1) AS value1, MAX(value2) AS value2, value3, value4
                        FROM m_constant_localized
                      WHERE constant_no IN (SELECT constant_no FROM m_constant WHERE key_string = ''PITCH_FOLLOW_BID_PRICE'')
                        AND language_code = ''ja''
                      GROUP BY value3, value4
                    ) AUTO_PITCH
                    WHERE (AUTO_PITCH.value3 IS NULL OR LENGTH(AUTO_PITCH.value3) = 0 OR (f_isnumeric(AUTO_PITCH.value3) AND COALESCE($1, 0) >= COALESCE(NULLIF(AUTO_PITCH.value3, '''')::numeric, 0)))
                      AND (AUTO_PITCH.value4 IS NULL OR LENGTH(AUTO_PITCH.value4) = 0 OR (f_isnumeric(AUTO_PITCH.value4) AND COALESCE($1, 0) < COALESCE(NULLIF(AUTO_PITCH.value4, '''')::numeric, 0)))
                    ';
      auto_pitch_sql := 'SELECT A.pitch_width FROM (' || auto_pitch_data_sql || ') A';
      input_price_pitch_low := 0;
      IF exhibition_rec.pitch_option = 1 THEN
        -- 指定単位の場合
        input_price_pitch_width := exhibition_rec.pitch_width;
        top_price_pitch_width := exhibition_rec.pitch_width;
        current_price_pitch_width := exhibition_rec.pitch_width;
      ELSE
          -- 入札価格帯の場合
          EXECUTE auto_pitch_data_sql
            INTO input_price_pitch_low, input_price_pitch_width
            USING price;

          -- Get pitch width by top_price
          EXECUTE auto_pitch_sql
            INTO top_price_pitch_width
            USING exhibition_rec.top_price;

          -- Get pitch width by current_price
          EXECUTE auto_pitch_sql
            INTO current_price_pitch_width
            USING exhibition_rec.current_price;
        END IF;

      --------------------------------------------------------------------------------------------
      -- 入力チェック
      --------------------------------------------------------------------------------------------

      -- 開催回設定情報(JSON→個別変数に分解)
      ext_auction_classification := exhibition_rec.exhibition_classification_info->'auctionClassification'; -- オークション方式(1:せり上げ、2:封印入札)
      ext_open_classification := exhibition_rec.exhibition_classification_info->'openClassification'; -- 公開区分（1:一般公開、2:特定の参加者）
      ext_bid_count_limit := exhibition_rec.exhibition_classification_info->'bidCountLimit'; -- 入札回数制限（0:制限なし、1:1回のみ）
      ext_bid_limit := exhibition_rec.exhibition_classification_info->'bidLimit'; -- 入札額制限（0:自由、1:前回自社入札より高い、2：現在価格より高い）
      ext_bid_min_useFlag := exhibition_rec.exhibition_classification_info->'bidMinUseFlag'; -- 最低入札価格使用有無（0:なし、1:あり）
      ext_bid_short_flag := exhibition_rec.exhibition_classification_info->'bidShortFlag'; -- 最低落札未達入力可否（0：入力不可、1:入力可）
      ext_bid_commit_min_use_flag := exhibition_rec.exhibition_classification_info->'bidCommitMinUseFlag'; -- 最低落札価格使用有無（0:なし、1:あり）
      ext_bid_commit_min_display_flag := exhibition_rec.exhibition_classification_info->'bidCommitMinDisplayFlag'; -- 最低落札表示有無フラグ（0:なし、1:あり）
      ext_bid_cancel_flag := exhibition_rec.exhibition_classification_info->'bidCancelFlag'; -- 入札取消可否（0：不可、1:入力可）
      -- ext_deal_flag := exhibition_rec.exhibition_classification_info->'dealFlag'; -- 即決有無（0:なし、1:あり）,TODO:  use COALESCE to handle null values cause dealFlag is null when query exhibition_rec, it seem constant PITCH_FOLLOW_BID_PRICE not properly set to this project
      ext_deal_flag := COALESCE((exhibition_rec.exhibition_classification_info->>'dealFlag')::integer, 0); -- 即決有無（0:なし、1:あり）
      ext_bid_commit_classification := exhibition_rec.exhibition_classification_info->'bidCommitClassification'; -- 落札価格決定区分（1:ファーストプライス、2:セカンドプライス）
      ext_bidder_commit_classification := exhibition_rec.exhibition_classification_info->'bidderCommitClassification'; -- 落札者決定区分（1:自動、2:手動設定）
      ext_bid_short_display_flag := exhibition_rec.exhibition_classification_info->'bidShortDisplayFlag'; -- 最低落札未達表示有無（0:なし、1:あり）
      ext_price_display_flag := exhibition_rec.exhibition_classification_info->'priceDisplayFlag'; -- 現在価格表示有無（0:なし、1:あり）
      ext_risk_adjustment_flag := exhibition_rec.exhibition_classification_info->'riskAdjustmentFlag'; -- リスク係数有無（0:なし、1:あり）
      ext_extend_flag := exhibition_rec.exhibition_classification_info->'extendFlag'; -- 延長有無（0:なし、1:あり）
      ext_bid_count_display_flag := exhibition_rec.exhibition_classification_info->'bidCountDisplayFlag'; -- 入札件数表示有無（0:なし、1:あり）
      ext_top_reduction_flag := exhibition_rec.exhibition_classification_info->'topReductionFlag'; -- TOP時引き下げ（0:なし、1:あり）

      -- 2022/10/21: Default終了時刻 > max_extend_datetimeの場合は延長なしと同じ
      IF exhibition_rec.default_end_datetime >= exhibition_rec.max_extend_datetime THEN
        ext_extend_flag := 0;
      END IF;

      -- 開催回参加会員
      IF exhibition_rec.exhibition_no IS NOT NULL THEN
        temp_sql := 'SELECT exhibition_member_no
                       FROM t_exhibition_member
                      WHERE exhibition_no = $1
                        AND member_no = $2';
        EXECUTE temp_sql INTO exhibition_member_no USING exhibition_rec.exhibition_no, user_rec.member_no;
      END IF;

      -- 最低落札価格を設定
      lowest_bid_accept_price := exhibition_rec.lowest_bid_accept_price;

      -- 開催回が存在しない
      IF exhibition_rec.exhibition_no IS NULL THEN
        error_code := 'EXHIBITION_NOT_FOUND_ERROR';

      -- 出品が存在しない
      ELSEIF exhibition_rec.exhibition_item_no IS NULL OR exhibition_rec.exhibition_item_delete_flag = 1 THEN
        error_code := 'EXHIBITION_ITEM_NOT_FOUND_ERROR';

      -- 入札者のtenant_noと出品のtenant_noが一致しない
      ELSEIF NOT (exhibition_rec.tenant_no = user_rec.tenant_no) THEN
        error_code := 'TENANT_NOT_MATCH_ERROR';

      -- 会員の通貨と開催回の通貨が一致しない場合はエラー
      ELSEIF NOT (exhibition_rec.currency_id = user_rec.currency_id) THEN
        error_code := 'CURRENCY_NOT_MATCH_ERROR';

      -- 入札停止フラグが立っていたらエラー
      ELSEIF exhibition_rec.cancel_flag = 1 THEN
        error_code := 'BID_BANNED_ERROR';

      -- 入札可能な時間内かチェック （延長発生時は商品別に終了時刻がアップデートされる）
      ELSEIF (NOT(exhibition_rec.start_datetime <= current_datetime
                  AND current_datetime <= exhibition_rec.end_datetime
                  AND current_datetime <= GREATEST(exhibition_rec.default_end_datetime, exhibition_rec.max_extend_datetime))
             ) THEN
        error_code := 'PERIOD_ERROR';

      -- 落札結果確定済の場合(流札、落札)はエラー
      ELSEIF exhibition_rec.hummer_flag <> 0 THEN
        error_code := 'HUMMER_ERROR';

      -- 入札金額が0を下回る場合はエラー
      ELSEIF price < 0 THEN
        error_code := 'PRICE_FORMAT_ERROR';

      -- 最低入札価格使用有かつ最低入札価格未満はエラー
      ELSEIF price <> 0 AND ext_bid_min_useFlag = 1 AND price < exhibition_rec.lowest_bid_price THEN
        error_code := 'LOWER_THAN_LOWEST_BID_PRICE_ERROR';

      -- 最低落札価格未達入力不可で最低落札価格を下回る入力はエラー
      ELSEIF price <> 0 AND ext_bid_commit_min_use_flag = 1 AND ext_bid_short_flag = 0 AND price < lowest_bid_accept_price THEN
        error_code := 'LOWER_THAN_LOWEST_BID_ACCEPT_PRICE_ERROR';

      -- 最低ピッチ幅以下だとエラー(入札額/ピッチ幅で余り0以外)
      ELSEIF input_price_pitch_width <> 0 AND price <> 0 AND MOD(price - input_price_pitch_low, input_price_pitch_width) <> 0 THEN
        error_code := 'MIN_PITCH_ERROR';

      -- 公開区分（1:一般公開、 2:特定の参加者）
      ELSEIF ext_open_classification = 2  AND exhibition_member_no IS NULL THEN
        error_code := 'MEMBER_NOT_ALLOW_BIDDING_ERROR';

      ELSE
        -- 即決価格使用時に即決価格を超える入札があった場合は即決価格で置き換える
        IF ext_deal_flag = 1 AND exhibition_rec.deal_bid_price < price THEN
          price := exhibition_rec.deal_bid_price;
        END IF;

        ------------------------------------------------------------------------------------
        -- 自分の入札情報を取得
        ------------------------------------------------------------------------------------
        temp_sql := 'SELECT bid_no
                          , bid_price
                       FROM t_bid
                      WHERE exhibition_item_no = $1
                        AND member_no = $2';
        EXECUTE temp_sql
           INTO last_bid_no, last_bid_price
          USING exhibition_item_no, user_rec.member_no;

        -- 既に1度でも入札している場合
        IF last_bid_no IS NOT NULL THEN
          ------------------------------------------------------------------------------------
          -- 入札可能金額のチェック (TOPのみ入札金額を下げることが可能。*2位は下げると現在価格が変わるため不可。)
          ------------------------------------------------------------------------------------

          -- 入札金額チェック(入札取消不可にも関わらず入札金額0の場合)
          IF price = 0 THEN
            IF ext_bid_cancel_flag = 0 THEN
              error_code := 'BID_CANCEL_ERROR';
              error_flag := true;
            END IF;

          -- 入札回数制限チェック(入札取消である0はここでは通す)
          ELSEIF ext_bid_count_limit = 1 THEN
            error_code := 'BID_COUNT_LIMIT_EXCEED_ERROR';
            error_flag := true;

          -- TOPの場合
          ELSEIF exhibition_rec.top_member_no = user_rec.member_no AND ext_auction_classification = 1 THEN
            -- TOP時引き下げが不可の場合は引き下げさせない
            IF ext_top_reduction_flag = 0 THEN
              IF price <= last_bid_price THEN
                error_code := 'PRICE_TOO_LOW_THAN_PREV_ERROR';
                error_flag := true;
              END IF;
            ELSE
              -- 現在価格+1ピッチ未満の場合エラー
              -- または
              -- （価格を同じか下げようとしている） かつ （今回入力した価格の税抜き換算価格がセカンド税抜価格以下）の場合エラー *後者だけだとセカンドのほうがトップより高い場合に値上げしてるのにエラーになる
              IF price < exhibition_rec.current_price + current_price_pitch_width THEN
                error_code := 'PRICE_TOO_LOW_THAN_CURRENT_ERROR';
                error_flag := true;
              END IF;
            END IF;

          -- 入札額制限チェック（0:自由、1:前回自社入札より高い、2：現在価格より高い）
          ELSEIF ext_bid_limit = 1 AND price <= last_bid_price THEN
            error_code := 'BID_LIMIT_EXCEED_ERROR';
            error_flag := true;

          -- 入札額制限チェック（0:自由、1:前回自社入札より高い、2：現在価格より高い）
          ELSEIF ext_bid_limit = 2 AND price <= exhibition_rec.current_price THEN
            error_code := 'BID_LIMIT_CURRENT_PRICE_EXCEED_ERROR';
            error_flag := true;

          END IF;

          IF error_flag = false THEN

            -- 入札テーブルの更新 (税還付価格が同じ場合、更新日時はアップデートしない。早いもの勝ちの関係でTOPが前後する場合があるため。)
            temp_sql := 'WITH BID_TBL AS (
                           UPDATE t_bid
                              SET bid_price = $1
                                , bid_user_no = $2
                                , update_user_no = $2
                                , update_datetime = CASE WHEN bid_price = $1
                                                         THEN update_datetime
                                                         ELSE now()
                                                     END
                                , bid_quantity = $4
                            WHERE bid_no = $3
                            RETURNING tenant_no
                                    , exhibition_item_no
                                    , member_no
                                    , bid_no
                                    , bid_price
                                    , bid_user_no
                                    , create_user_no
                                    , create_datetime
                                    , update_user_no
                                    , update_datetime
                                    , bid_quantity
                         )
                   SELECT *
                     FROM BID_TBL';
            EXECUTE temp_sql
              INTO regist_bid_rec
              USING price
                  , in_bid_user_no
                  , last_bid_no
                  , temp_quantity;

          END IF;

        -- 1度も入札していない場合
        ELSE

          -- 入札取消可否に関わらず0円入札はできない
          IF price = 0 THEN
            error_code := 'BID_CANCEL_ERROR';
            error_flag := true;

          -- 入札額制限チェック（0:自由、1:前回自社入札より高い、2：現在価格より高い）
          ELSEIF ext_bid_limit = 1 AND price <= last_bid_price THEN
            error_code := 'BID_LIMIT_EXCEED_ERROR';
            error_flag := true;

          -- 入札額制限チェック（0:自由、1:前回自社入札より高い、2：現在価格より高い）
          ELSEIF ext_bid_limit = 2 AND price <= exhibition_rec.current_price THEN
            error_code := 'BID_LIMIT_CURRENT_PRICE_EXCEED_ERROR';
            error_flag := true;

          ELSE
            -- 入札情報の登録
            temp_sql := 'WITH BID_TBL AS (
                           INSERT INTO t_bid
                                     (
                                       tenant_no
                                     , exhibition_item_no
                                     , member_no
                                     , bid_price
                                     , bid_user_no
                                     , create_user_no
                                     , create_datetime
                                     , update_user_no
                                     , update_datetime
                                     , bid_quantity
                                     )
                                VALUES
                                     (
                                       $1
                                     , $2
                                     , $3
                                     , $4
                                     , $5
                                     , $5
                                     , now()
                                     , $5
                                     , now()
                                     , $6
                                     )
                           RETURNING
                             tenant_no
                           , exhibition_item_no
                           , member_no
                           , bid_no
                           , bid_price
                           , bid_user_no
                           , create_user_no
                           , create_datetime
                           , update_user_no
                           , update_datetime
                           , bid_quantity
                         )
                         SELECT *
                           FROM BID_TBL';
            EXECUTE temp_sql
              INTO regist_bid_rec
              USING
                exhibition_rec.tenant_no
              , exhibition_item_no
              , user_rec.member_no
              , price
              , in_bid_user_no
              , temp_quantity;
          END IF;

        END IF;

        IF error_flag = false THEN
          -- 入札取消
          IF price = 0 THEN
            temp_sql := 'SELECT * FROM "f_get_current_price"($1)';
            EXECUTE temp_sql INTO after_top_data_rec USING exhibition_item_no;

          ELSE
            ------------------------------------------------------------------------------------
            -- この入札によってトップまたは現在価格が替わったかチェックする
            ------------------------------------------------------------------------------------
            -- 現在TOPの入札者、価格を取得する
            IF ext_bid_commit_classification = 1 THEN

              -- 競り上げの場合
              IF ext_auction_classification = 1 THEN

                IF price > exhibition_rec.top_price THEN
                  -- TOP者、現在価格が変わる
                  temp_sql := 'SELECT $1 AS top_member_no,
                                      $2 AS current_price,
                                      $3 AS top_price';
                  EXECUTE temp_sql
                    INTO after_top_data_rec
                    USING
                      user_rec.member_no
                    , price
                    , price;
                ELSE
                  -- TOP者、現在価格が変わらない
                  temp_sql := 'SELECT $1 AS top_member_no,
                                      $2 AS current_price,
                                      $3 AS top_price';
                  EXECUTE temp_sql
                    INTO after_top_data_rec
                    USING
                      exhibition_rec.top_member_no
                    , exhibition_rec.current_price
                    , exhibition_rec.top_price;
                END IF;
              ELSE
                -- 封印の場合
                temp_sql := 'SELECT TB.member_no AS top_member_no,
                                    TB.bid_price AS current_price,
                                    TB.bid_price AS top_price
                               FROM t_bid TB
                                    LEFT JOIN t_exhibition_item TEI
                                           ON TB.exhibition_item_no = TEI.exhibition_item_no
                                        WHERE TB.exhibition_item_no = $1
                                          AND TB.bid_price >= TEI.lowest_bid_price
                                        ORDER BY TB.bid_price DESC, TB.update_datetime
                                        LIMIT 1';
                  EXECUTE temp_sql
                    INTO after_top_data_rec
                    USING
                      exhibition_item_no;
              END IF;

            ELSE
              -- セカンドプライス
              IF price > exhibition_rec.top_price  AND price <= exhibition_rec.lowest_bid_accept_price THEN
                temp_sql := 'SELECT $1 AS top_member_no,
                                    $2 AS current_price,
                                    $3 AS top_price';
                EXECUTE temp_sql
                  INTO after_top_data_rec
                  USING
                    user_rec.member_no
                  , price
                  , price;

              ELSEIF user_rec.member_no != exhibition_rec.top_member_no THEN
                IF price > exhibition_rec.top_price AND price >= exhibition_rec.lowest_bid_accept_price THEN
                  -- TOP者、現在価格が変わる
                  temp_sql := 'SELECT $1 AS top_member_no,
                                      $2 AS current_price,
                                      $3 AS top_price';
                  EXECUTE temp_sql
                    INTO after_top_data_rec
                    USING
                      user_rec.member_no
                    , GREATEST(exhibition_rec.top_price + top_price_pitch_width, exhibition_rec.lowest_bid_accept_price)
                    , price;

                ELSEIF price + input_price_pitch_width > exhibition_rec.current_price THEN
                  -- 現在価格が変わるが、TOP者が変わらない
                  temp_sql := 'SELECT $1 AS top_member_no,
                                      $2 AS current_price,
                                      $3 AS top_price';
                  EXECUTE temp_sql
                    INTO after_top_data_rec
                    USING
                      exhibition_rec.top_member_no
                    , LEAST(exhibition_rec.top_price, price + input_price_pitch_width)
                    , exhibition_rec.top_price;

                ELSE
                  -- TOP者、現在価格が変わらない
                  temp_sql := 'SELECT $1 AS top_member_no,
                                      $2 AS current_price,
                                      $3 AS top_price';
                  EXECUTE temp_sql
                    INTO after_top_data_rec
                    USING
                      exhibition_rec.top_member_no
                    , exhibition_rec.current_price
                    , exhibition_rec.top_price;

                END IF;

              ELSEIF user_rec.member_no = exhibition_rec.top_member_no THEN
                temp_sql := 'SELECT MAX(bid_price)
                              FROM t_bid
                              WHERE exhibition_item_no = $1
                                AND member_no <> $2';
                EXECUTE temp_sql
                  INTO second_price
                  USING
                    exhibition_item_no
                  , exhibition_rec.top_member_no
                ;

                IF second_price IS NOT NULL AND second_price >= exhibition_rec.lowest_bid_accept_price THEN

                  -- Get pitch width by second_price
                  IF exhibition_rec.pitch_option = 1 THEN
                    -- 指定単位の場合
                    second_price_pitch_width := exhibition_rec.pitch_width;
                  ELSE
                    -- 入札価格帯の場合
                    EXECUTE auto_pitch_sql
                      INTO second_price_pitch_width
                      USING second_price;
                  END IF;

                  -- 現在価格が変わるがTOPが変わらない
                  temp_sql := 'SELECT $1 AS top_member_no,
                                      $2 AS current_price,
                                      $3 AS top_price';
                          EXECUTE temp_sql
                            INTO after_top_data_rec
                            USING
                              exhibition_rec.top_member_no
                            , LEAST(price, second_price + second_price_pitch_width)
                            , price;

                ELSE
                  -- TOP価格が変わるだけ
                  -- temp_sql := 'SELECT $1 AS top_member_no,
                  --                     $2 AS current_price,
                  --                     $3 AS top_price';
                  -- EXECUTE temp_sql
                  --   INTO after_top_data_rec
                  --   USING
                  --     exhibition_rec.top_member_no
                  --   , exhibition_rec.current_price
                  --   , price;
                  IF exhibition_rec.top_price < exhibition_rec.lowest_bid_accept_price AND price >= exhibition_rec.lowest_bid_accept_price THEN
                    temp_sql := 'SELECT $1 AS top_member_no,
                                        $2 AS current_price,
                                        $3 AS top_price';
                    EXECUTE temp_sql
                      INTO after_top_data_rec
                      USING
                        exhibition_rec.top_member_no
                      , exhibition_rec.lowest_bid_accept_price
                      , price;
                  ELSE
                    temp_sql := 'SELECT $1 AS top_member_no,
                                        $2 AS current_price,
                                        $3 AS top_price';
                    EXECUTE temp_sql
                      INTO after_top_data_rec
                      USING
                      exhibition_rec.top_member_no
                    , exhibition_rec.current_price
                    , price;
                  END IF;
                END IF;
              END IF;
            END IF;
          END IF;

          IF ext_auction_classification = 1 THEN
            -- 2022/10/28: TOP交代場合のみ延長する
            -- IF COALESCE(exhibition_rec.top_member_no,-1) <> COALESCE(after_top_data_rec.top_member_no, -1) THEN

            --   top_or_current_price_changed_flag := true;

            -- END IF;
            IF COALESCE(exhibition_rec.top_member_no,-1) <> COALESCE(after_top_data_rec.top_member_no, -1)
                OR COALESCE(exhibition_rec.current_price,-1) <> COALESCE(after_top_data_rec.current_price, -1) THEN

              top_or_current_price_changed_flag := true;

            END IF;

            -- 出品商品テーブルのTOP会員、現在価格を更新
            temp_sql := 'UPDATE t_exhibition_item
                            SET top_member_no = $1
                              , current_price = $2
                              , top_price = $3
                          WHERE exhibition_item_no = $4';
            EXECUTE temp_sql
              USING after_top_data_rec.top_member_no
                  , after_top_data_rec.current_price
                  , after_top_data_rec.top_price
                  , exhibition_item_no;

            -- TOPと２番手(Socket送信用)
            temp_sql := 'SELECT BH.member_no
                          FROM (
                          SELECT TEI.exhibition_item_no, TB.member_no, TB.bid_price, TB.create_datetime,
                                  ROW_NUMBER() OVER (PARTITION BY TEI.exhibition_item_no, TB.bid_price ORDER BY TB.create_datetime ASC) AS row_number
                           FROM t_exhibition_item TEI
                           LEFT JOIN t_bid TB
                             ON TB.exhibition_item_no = TEI.exhibition_item_no
                            AND TB.tenant_no = TEI.tenant_no
                           LEFT JOIN (
                            SELECT MAX(value1) AS value1, MAX(value2) AS value2, value3, value4
                              FROM m_constant_localized
                            WHERE constant_no IN (SELECT constant_no FROM m_constant WHERE key_string=''PITCH_FOLLOW_BID_PRICE'')
                              AND language_code = ''ja''
                            GROUP BY value3, value4
                           ) AUTO_PITCH_BID_PRICE
                             ON (AUTO_PITCH_BID_PRICE.value3 IS NULL OR LENGTH(AUTO_PITCH_BID_PRICE.value3) = 0 OR COALESCE(TB.bid_price, TEI.lowest_bid_price) >= COALESCE(NULLIF(AUTO_PITCH_BID_PRICE.value3, '')::numeric, 0))
                            AND (AUTO_PITCH_BID_PRICE.value4 IS NULL OR LENGTH(AUTO_PITCH_BID_PRICE.value4) = 0 OR COALESCE(TB.bid_price, TEI.lowest_bid_price) < COALESCE(NULLIF(AUTO_PITCH_BID_PRICE.value4, '')::numeric, 0))
                          WHERE TEI.exhibition_item_no=$1
                            AND COALESCE(TEI.top_member_no, -1) <> TB.member_no
                            AND TEI.lowest_bid_accept_price <= TB.bid_price
                            AND (
                              TB.bid_price +
                              CASE
                                WHEN $2 = 1 THEN $3
                                ELSE COALESCE(NULLIF(AUTO_PITCH_BID_PRICE.value1, '''')::numeric, 0)
                              END
                            ) >= TEI.current_price
                        ) AS BH
                        WHERE BH.row_number = 1
                      ';
            EXECUTE temp_sql
              INTO second_bidder_member_no
              USING
                exhibition_item_no,
                exhibition_rec.pitch_option, -- pitch_option : 1 => 指定単位(例：固定 1) , 2: 入札価格帯(例：＜5万、＞10万)
                exhibition_rec.pitch_width;
            tmp_top_text := '{
              "exhibition_item_no":"' || exhibition_item_no || '",
              "second_member":' || COALESCE(second_bidder_member_no, -1) || ',
              "top_member":' || CASE WHEN after_top_data_rec.top_price >= exhibition_rec.lowest_bid_accept_price AND after_top_data_rec.top_member_no IS NOT NULL
                    THEN '"' || after_top_data_rec.top_member_no || '"'
                    ELSE 'null' END ||
            '}';
            IF top_2nd_text <> '' THEN
              top_2nd_text := top_2nd_text || ',';
            END IF;
            top_2nd_text := top_2nd_text || tmp_top_text;

            -- TOP交代情報を戻す(メール通知用)
            IF COALESCE(exhibition_rec.top_member_no,-1) <> COALESCE(after_top_data_rec.top_member_no, -1) AND exhibition_rec.lowest_bid_accept_price <= after_top_data_rec.current_price AND exhibition_rec.cancel_flag = 0 THEN
              IF top_changed_text <> '' THEN
                top_changed_text := top_changed_text || ',';
              END IF;
              tmp_top_text := '{
                "exhibition_item_no":"' || exhibition_item_no || '",
                "before_top_member":' || CASE WHEN exhibition_rec.top_member_no IS NOT NULL
                    THEN '"' || exhibition_rec.top_member_no || '"'
                    ELSE 'null' END || ',
                "before_top_bid_user_name":"' || COALESCE(exhibition_rec.top_bid_user_name, '') || '",
                "before_top_member_top_price_exceeded":' || COALESCE(exhibition_rec.top_member_no IS NOT NULL
                    AND COALESCE(exhibition_rec.top_price,-1) >= exhibition_rec.lowest_bid_accept_price, FALSE)::character varying || ',
                "after_top_member":' || CASE WHEN after_top_data_rec.top_member_no IS NOT NULL
                      THEN '"' || after_top_data_rec.top_member_no || '"'
                      ELSE 'null' END ||
              '}';
              top_changed_text := top_changed_text || tmp_top_text;
              top_changed_count = top_changed_count + 1;
            END IF;

          END IF;
          ------------------------------------------------------------------------------------
          -- 即決処理: 即決価格を超える入札があった場合
          ------------------------------------------------------------------------------------
          IF ext_deal_flag = 1 AND exhibition_rec.deal_bid_price IS NOT NULL AND exhibition_rec.deal_bid_price <= price THEN
            temp_sql := 'UPDATE t_exhibition_item
                            SET current_price = $1
                              , bid_success_member_no = $2
                              , top_member_no = $2
                              , end_datetime = now()
                              , default_end_datetime = now()
                          WHERE exhibition_item_no = $3';
            EXECUTE temp_sql
              USING
              price,
              after_top_data_rec.top_member_no,
              exhibition_item_no;
          END IF;
          ------------------------------------------------------------------------------------
          -- 延長処理： セリ終了N分前かつセリ最大延長時刻M分前の間の入札でTOP交代または現在価格が変わった場合
          ------------------------------------------------------------------------------------
          after_bid_end_datetime := exhibition_rec.end_datetime;

          IF ext_extend_flag = 1 AND top_or_current_price_changed_flag = true
            AND NOT(ext_deal_flag = 1 AND exhibition_rec.deal_bid_price <= price)
            AND current_datetime > exhibition_rec.end_datetime - CAST( exhibition_rec.extend_judge_minutes || ' minutes' AS interval ) THEN

            IF current_datetime < exhibition_rec.max_extend_datetime - CAST( exhibition_rec.extend_minutes || ' minutes' AS interval ) THEN
              RAISE NOTICE '延長処理実行==%' , current_datetime + CAST( exhibition_rec.extend_minutes || ' minutes' AS interval );

              temp_sql := 'UPDATE t_exhibition_item
                              SET end_datetime = $1
                            WHERE exhibition_item_no = $2
                            RETURNING end_datetime';
              EXECUTE temp_sql
                INTO after_bid_end_datetime
                USING
                  CASE WHEN current_datetime + CAST( exhibition_rec.extend_minutes || ' minutes' AS interval ) > exhibition_rec.end_datetime
                       THEN to_timestamp(to_char((current_datetime + CAST( exhibition_rec.extend_minutes || ' minutes' AS interval )), 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'), 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
                       ELSE exhibition_rec.end_datetime
                   END
                  ,
                  exhibition_item_no;
            ELSE
              -- 最大延長時刻到達
              RAISE NOTICE '延長処理実行（最大延長時刻）==%' , exhibition_rec.max_extend_datetime;

              temp_sql := 'UPDATE t_exhibition_item
                              SET end_datetime = $1
                            WHERE exhibition_item_no = $2
                            RETURNING end_datetime';
              EXECUTE temp_sql
                INTO after_bid_end_datetime
                USING
                  exhibition_rec.max_extend_datetime,
                  exhibition_item_no;
            END IF;

          END IF;

          ------------------------------------------------------------------------------------
          -- 入札履歴テーブルに一件インサート
          ------------------------------------------------------------------------------------
          temp_sql := 'INSERT INTO t_bid_history
                                  (
                                    tenant_no
                                  , exhibition_item_no
                                  , member_no
                                  , bid_price
                                  , after_top_member_no
                                  , after_current_price
                                  , bid_user_no
                                  , bid_user_name
                                  , bid_nickname
                                  , create_user_no
                                  , create_datetime
                                  , bid_quantity
                                  )
                              VALUES
                                  (
                                    $1
                                  , $2
                                  , $3
                                  , $4
                                  , $5
                                  , $6
                                  , $7
                                  , $8
                                  , $9
                                  , $10
                                  , now()
                                  , $11
                                  )';
          EXECUTE temp_sql
            USING
              exhibition_rec.tenant_no
            , exhibition_item_no
            , user_rec.member_no
            , price
            , after_top_data_rec.top_member_no
            , CASE WHEN ext_deal_flag = 1 AND exhibition_rec.deal_bid_price <= price THEN
                price
              ELSE
                after_top_data_rec.current_price
              END
            , in_bid_user_no
            , in_user_name
            , user_rec.nickname
            , in_bid_user_no
            , temp_quantity;


            ------------------------------------------------------------------------------------
            -- bid_countのカウントアップ
            ------------------------------------------------------------------------------------
            temp_sql := 'UPDATE t_exhibition_item
                            SET bid_count = COALESCE(bid_count, 0) + 1
                          WHERE exhibition_item_no = $1';
            EXECUTE temp_sql
              USING
                exhibition_item_no;

            ------------------------------------------------------------------------------------
            -- 開催回サマリーbid_countのカウントアップ
            ------------------------------------------------------------------------------------
            temp_sql := 'UPDATE t_exhibition_summary
                            SET bid_count = COALESCE(bid_count, 0) + 1
                          WHERE exhibition_no = $1';
            EXECUTE temp_sql
              USING
                exhibition_rec.exhibition_no;
        END IF;
      END IF;
    END IF;

    IF error_code = '' THEN
      -- Get pitch width by after_top_data_rec.top_price
      IF exhibition_rec.pitch_option = 1 THEN
        -- 指定単位の場合
        after_top_top_price_pitch_width := exhibition_rec.pitch_width;
      ELSE
        -- 入札価格帯の場合
        EXECUTE auto_pitch_sql
          INTO after_top_top_price_pitch_width
          USING after_top_data_rec.top_price;
      END IF;

      IF ext_auction_classification = 1 THEN
        temp_text := '{
          "category_id": "' || exhibition_rec.category_id || '",
          "exhibition_item_no":"' || exhibition_item_no || '",
          "auction_classification":"' || ext_auction_classification || '",
          "current_price":"' || COALESCE(after_top_data_rec.current_price::character varying, exhibition_rec.lowest_bid_price::character varying) || '",
          "is_top_member":' || COALESCE(after_top_data_rec.top_member_no = user_rec.member_no AND exhibition_rec.lowest_bid_accept_price <= after_top_data_rec.current_price AND exhibition_rec.cancel_flag = 0, FALSE)::character varying || ',
          "is_second_member":' || COALESCE(after_top_data_rec.top_member_no <> user_rec.member_no AND exhibition_rec.lowest_bid_accept_price <= regist_bid_rec.bid_price AND (regist_bid_rec.bid_price + input_price_pitch_width) >= after_top_data_rec.current_price AND exhibition_rec.cancel_flag = 0, FALSE)::character varying || ',
          "is_exceeding_lowest_price":' || COALESCE(after_top_data_rec.top_member_no IS NOT NULL AND exhibition_rec.lowest_bid_accept_price <= after_top_data_rec.current_price AND exhibition_rec.cancel_flag = 0, FALSE)::character varying || ',
          "is_not_exceeding_lowest_price":' || COALESCE(after_top_data_rec.top_member_no IS NOT NULL AND after_top_data_rec.current_price <> exhibition_rec.lowest_bid_price AND exhibition_rec.lowest_bid_accept_price > after_top_data_rec.current_price AND exhibition_rec.cancel_flag = 0, FALSE)::character varying || ',
          "is_more_little":' || COALESCE(after_top_data_rec.top_member_no IS NOT NULL AND exhibition_rec.lowest_bid_accept_price > COALESCE(after_top_data_rec.top_price, 0) AND exhibition_rec.lowest_bid_accept_price <= (COALESCE(after_top_data_rec.top_price, 0) + COALESCE(exhibition_rec.more_little_judge_pitch, 0)*after_top_top_price_pitch_width) AND exhibition_rec.cancel_flag = 0, FALSE)::character varying || ',
          "is_cancel":' || COALESCE(exhibition_rec.cancel_flag = 1, FALSE)::character varying || ',
          "status": ' || CASE WHEN COALESCE(after_top_data_rec.top_member_no = user_rec.member_no AND exhibition_rec.lowest_bid_accept_price <= regist_bid_rec.bid_price, FALSE) THEN 3 WHEN regist_bid_rec.bid_price = 0 THEN 0 ELSE 1 END || ',
          "bid_price":"' || COALESCE(regist_bid_rec.bid_price::character varying, '') || '",
          "bid_quantity":"' || COALESCE(regist_bid_rec.bid_quantity::character varying, '1') || '",
          "extending":' || COALESCE(after_bid_end_datetime > exhibition_rec.end_datetime, FALSE)::character varying || ',
          "remaining_seconds":' || ((DATE_PART('day', after_bid_end_datetime - now()) * 24 + DATE_PART('hour', after_bid_end_datetime - now())) * 60 + DATE_PART('minute', after_bid_end_datetime - now())) * 60 + DATE_PART('second', after_bid_end_datetime - now()) || '
        }';
      ELSE
        temp_text := '{
          "category_id": "' || exhibition_rec.category_id || '",
          "exhibition_item_no":"' || exhibition_item_no || '",
          "auction_classification":"' || ext_auction_classification || '",
          "status": ' || CASE WHEN regist_bid_rec.bid_price = 0 THEN 0 ELSE 1 END || ',
          "bid_price":"' || COALESCE(regist_bid_rec.bid_price::character varying, '') || '",
          "extending":' || COALESCE(after_bid_end_datetime > exhibition_rec.end_datetime, FALSE)::character varying || ',
          "remaining_seconds":' || ((DATE_PART('day', after_bid_end_datetime - now()) * 24 + DATE_PART('hour', after_bid_end_datetime - now())) * 60 + DATE_PART('minute', after_bid_end_datetime - now())) * 60 + DATE_PART('second', after_bid_end_datetime - now()) || '
        }';
      END IF;
    ELSE
      temp_text := '{"exhibition_item_no":"' || COALESCE(exhibition_item_no::character varying, temp_exhibition_item_no, '') || '", "errorMessage":"' || error_code || '"}';
      error_count := error_count + 1;
      status := 200;
    END IF;

    IF result_text <> '' THEN
      result_text := COALESCE(result_text, '') || ',';
    END IF;

    result_text := COALESCE(result_text, '') || temp_text;

  END LOOP;

  RAISE NOTICE 'BID LOOP終了';

  ----------------------------------------------------------------------------------------------------
  -- return OK
  ----------------------------------------------------------------------------------------------------

  temp_text := format('{"result":{"statusCode":%s, "errorCount":%I, "bidList": %s, "topChangeCount":%I, "topChangeList": %s, "topAndSecondList": %s}}',
                 status, error_count, '[' || COALESCE(result_text, '') || ']', top_changed_count, '[' || top_changed_text || ']', '[' || top_2nd_text || ']');

  result_data := temp_text::json;

  RAISE NOTICE 'BID正常終了==%' , temp_text::json;

  RETURN result_data;

END;

$BODY$;
