CREATE OR REPLACE FUNCTION public.f_regist_bid_sealed
(
    in_bid_user_no bigint,
    in_user_name character varying,
    in_bid_items json
)
RETURNS json
LANGUAGE plpgsql
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 入札をN件登録する（開催回を複数またがっていても良い）
-- Parameters
-- @param in_bid_user_no 利用者番号
-- @param in_user_name 使わない
-- @param in_bid_items 入札内容JSON（exhibitionItemNo:出品番号、bidPrice:入札金額、bidQuantity:入札数量
----------------------------------------------------------------------------------------------------
  user_rec record; -- 利用者情報
  regist_bid_rec record; -- 入札情報(今回登録分)
  exhibition_rec record; -- 開催回情報
  temp_sql text = '';
  temp_sql_where character varying = '';
  temp_text text = '';
  temp_exhibition_item_no character varying;
  temp_price character varying;
  temp_quantity character varying;
  temp_json json;
  ext_auction_classification integer;
  ext_open_classification integer;
  ext_bid_count_limit integer;
  ext_bid_limit integer;
  ext_bid_min_useFlag integer;
  ext_bid_short_flag integer;
  ext_bid_commit_min_use_flag integer;
  ext_bid_commit_min_display_flag integer;
  ext_bid_cancel_flag integer;
  ext_deal_flag integer;
  ext_bid_commit_classification integer;
  ext_bidder_commit_classification integer;
  ext_bid_short_display_flag integer;
  ext_bid_count_display_flag integer;
  ext_price_display_flag integer;
  ext_risk_adjustment_flag integer;
  ext_extend_flag integer;
  ext_top_reduction_flag integer;
  last_bid_no bigint;
  last_bid_price numeric;
  bid_count integer = 0;
  exhibition_item_no bigint;
  exhibition_id bigint;
  current_datetime timestamp without time zone;
  start_datetime timestamp without time zone;
  end_datetime timestamp without time zone;
  price numeric;
  quantity integer;
  tax numeric;
  error_flag boolean;
  error_count integer = 0;
  error_code character varying = '';
  error_message character varying = '';
  system_error_message character varying = '';
  result_data json;
  result_text text = '';
  status integer = 200;
  after_bid_end_datetime timestamp without time zone;

  BEGIN
  temp_sql := 'SELECT MM.member_no,
                      NULL AS nickname,
                      MM.currency_id,
                      MU.bid_allow_flag AS user_bid_allow_flag,
                      MM.bid_allow_flag AS member_bid_allow_flag,
                      MU.tenant_no
            FROM m_user MU
            LEFT JOIN m_member MM
              ON MM.member_no = MU.member_no
           WHERE MU.user_no = $1
             AND MU.delete_flag = 0
             AND MM.delete_flag = 0';
  EXECUTE temp_sql INTO user_rec USING in_bid_user_no;
  IF user_rec.user_bid_allow_flag = 0 OR user_rec.member_bid_allow_flag = 0 THEN
    error_code := 'MEMBER_NOT_ALLOW_BIDDING_ERROR';
  END IF;
  IF error_code <> '' THEN
    RAISE EXCEPTION '%', error_code;
  END IF;
  in_bid_items := to_json(in_bid_items);
  FOR temp_json IN SELECT * FROM json_array_elements(in_bid_items)
  LOOP
    IF temp_json ->> 'exhibitionItemNo' IS NOT NULL AND temp_json ->> 'exhibitionItemNo' <> '' THEN
      temp_exhibition_item_no := replace(temp_json ->> 'exhibitionItemNo', '"', '');
      IF bid_count <> 0 THEN
        temp_sql_where := temp_sql_where || ' OR ';
      END IF;
      IF NOT temp_exhibition_item_no :: text ~ '^([1-9]\d*|0)$' THEN
        temp_exhibition_item_no := '0';
      END IF;
      temp_sql_where := temp_sql_where || 'exhibition_item_no = ' || temp_exhibition_item_no;
      bid_count := bid_count + 1;
    END IF;
  END LOOP;
  IF temp_sql_where <> '' THEN
    temp_sql := 'SELECT 1
                   FROM t_exhibition_item
                  WHERE ' || temp_sql_where || '
                 FOR UPDATE';
    EXECUTE temp_sql;
  END IF;
  FOR temp_json IN SELECT * FROM json_array_elements(in_bid_items)
  LOOP
    error_code := '';
    SELECT CURRENT_TIMESTAMP
      INTO current_datetime;
    temp_exhibition_item_no := temp_json ->> 'exhibitionItemNo';
    temp_price := temp_json ->> 'bidPrice';
    temp_quantity := temp_json ->> 'bidQuantity';
    IF temp_exhibition_item_no IS NULL OR temp_exhibition_item_no = '' THEN
      error_code := 'EXHIBITION_ITEM_REQUIRED_ERROR';
    ELSEIF NOT temp_exhibition_item_no :: text ~ '^([1-9]\d*|0)$' THEN
      error_code := 'EXHIBITION_ITEM_FORMAT_ERROR';
    ELSEIF temp_price IS NULL OR temp_price = '' THEN
      error_code := 'PRICE_REQUIRED_ERROR';
    ELSEIF NOT temp_price :: text ~ '^([0-9]\d{0,9})(\.\d{0,2})?$' THEN
      error_code := 'PRICE_FORMAT_ERROR';
    ELSEIF temp_quantity IS NULL OR temp_quantity = '' THEN
      error_code := 'QUANTITY_REQUIRED_ERROR';
    ELSEIF NOT temp_quantity :: text ~ '^([0-9]\d{0,9})(\.\d{0,2})?$' THEN
      error_code := 'QUANTITY_FORMAT_ERROR';
    ELSE
      error_flag := false;
      exhibition_item_no := replace(temp_exhibition_item_no, '"', '');
      price := replace(temp_price, '"', '');
      quantity := replace(temp_quantity, '"', '');
      temp_sql := 'SELECT B.tenant_no
                        , C.category_id
                        , B.exhibition_item_no
                        , (CASE
                            WHEN B.end_datetime > COALESCE(B.default_end_datetime, B.end_datetime) THEN B.end_datetime
                            ELSE COALESCE(B.default_end_datetime, B.end_datetime)
                          END) AS end_datetime
                        , B.default_end_datetime
                        , C.pitch_option
                        , COALESCE(B.lowest_bid_price, 0) AS lowest_bid_price
                        , COALESCE(B.lowest_bid_accept_price, 0) AS lowest_bid_accept_price
                        , COALESCE(B.quantity, 0) AS quantity
                        , COALESCE(B.lowest_bid_quantity, 0) AS lowest_bid_quantity
                        , COALESCE(B.deal_bid_price, 0) AS deal_bid_price
                        , COALESCE(B.top_member_no, -1) AS top_member_no
                        , TBH.bid_user_name AS top_bid_user_name
                        , COALESCE(B.top_price, 0) AS top_price
                        , COALESCE(B.current_price, 0) AS current_price
                        , B.exhibition_member_no
                        , B.hummer_flag
                        , B.cancel_flag
                        , B.delete_flag exhibition_item_delete_flag
                        , C.exhibition_no
                        , C.start_datetime
                        , C.max_extend_datetime
                        , C.extend_judge_minutes
                        , C.extend_minutes
                        , C.currency_id
                        , C.exhibition_classification_info
                        , C.more_little_judge_pitch
                     FROM t_exhibition_item B
                          LEFT OUTER JOIN t_exhibition C
                                       ON B.exhibition_no = C.exhibition_no
                                      AND C.delete_flag = 0
                          LEFT JOIN t_bid_history TBH
                                 ON TBH.exhibition_item_no = B.exhibition_item_no
                                AND TBH.member_no = B.top_member_no
                                AND TBH.bid_price = B.top_price
                    WHERE B.exhibition_item_no = $1
                    ORDER BY TBH.create_datetime DESC
                    LIMIT 1';
      EXECUTE temp_sql INTO exhibition_rec USING exhibition_item_no, 'PITCH_FOLLOW_BID_PRICE', 'ja';

      ext_auction_classification := (exhibition_rec.exhibition_classification_info->>'auctionClassification')::integer; -- オークション方式(1:せり上げ、2:封印入札)
      ext_open_classification := (exhibition_rec.exhibition_classification_info->>'openClassification')::integer; -- 公開区分（1:一般公開、2:特定の参加者）
      ext_bid_count_limit := (exhibition_rec.exhibition_classification_info->>'bidCountLimit')::integer; -- 入札回数制限（0:制限なし、1:1回のみ）
      ext_bid_limit := (exhibition_rec.exhibition_classification_info->>'bidLimit')::integer; -- 入札額制限（0:自由、1:前回自社入札より高い、2：現在価格より高い）
      ext_bid_min_useFlag := (exhibition_rec.exhibition_classification_info->>'bidMinUseFlag')::integer; -- 最低入札価格使用有無（0:なし、1:あり）
      ext_bid_short_flag := (exhibition_rec.exhibition_classification_info->>'bidShortFlag')::integer; -- 最低落札未達入力可否（0：入力不可、1:入力可）
      ext_bid_commit_min_use_flag := (exhibition_rec.exhibition_classification_info->>'bidCommitMinUseFlag')::integer; -- 最低落札価格使用有無（0:なし、1:あり）
      ext_bid_commit_min_display_flag := (exhibition_rec.exhibition_classification_info->>'bidCommitMinDisplayFlag')::integer; -- 最低落札表示有無フラグ（0:なし、1:あり）
      ext_bid_cancel_flag := (exhibition_rec.exhibition_classification_info->>'bidCancelFlag')::integer; -- 入札取消可否（0：不可、1:入力可）
      ext_deal_flag := (exhibition_rec.exhibition_classification_info->>'dealFlag')::integer; -- 即決有無（0:なし、1:あり）
      ext_bid_commit_classification := (exhibition_rec.exhibition_classification_info->>'bidCommitClassification')::integer; -- 落札価格決定区分（1:ファーストプライス、2:セカンドプライス）
      ext_bidder_commit_classification := (exhibition_rec.exhibition_classification_info->>'bidderCommitClassification')::integer; -- 落札者決定区分（1:自動、2:手動設定）
      ext_bid_short_display_flag := (exhibition_rec.exhibition_classification_info->>'bidShortDisplayFlag')::integer; -- 最低落札未達表示有無（0:なし、1:あり）
      ext_price_display_flag := (exhibition_rec.exhibition_classification_info->>'priceDisplayFlag')::integer; -- 現在価格表示有無（0:なし、1:あり）
      ext_risk_adjustment_flag := (exhibition_rec.exhibition_classification_info->>'riskAdjustmentFlag')::integer; -- リスク係数有無（0:なし、1:あり）
      ext_extend_flag := (exhibition_rec.exhibition_classification_info->>'extendFlag')::integer; -- 延長有無（0:なし、1:あり）
      ext_bid_count_display_flag := (exhibition_rec.exhibition_classification_info->>'bidCountDisplayFlag')::integer; -- 入札件数表示有無（0:なし、1:あり）
      ext_top_reduction_flag := (exhibition_rec.exhibition_classification_info->>'topReductionFlag')::integer; -- TOP時引き下げ（0:なし、1:あり）

      IF exhibition_rec.exhibition_no IS NULL THEN
        error_code := 'EXHIBITION_NOT_FOUND_ERROR';
      ELSEIF exhibition_rec.exhibition_item_no IS NULL OR exhibition_rec.exhibition_item_delete_flag = 1 THEN
        error_code := 'EXHIBITION_ITEM_NOT_FOUND_ERROR';
      ELSEIF NOT (exhibition_rec.tenant_no = user_rec.tenant_no) THEN
        error_code := 'TENANT_NOT_MATCH_ERROR';
      ELSEIF NOT (exhibition_rec.currency_id = user_rec.currency_id) THEN
        error_code := 'CURRENCY_NOT_MATCH_ERROR';
      ELSEIF exhibition_rec.cancel_flag = 1 THEN
        error_code := 'BID_BANNED_ERROR';
      ELSEIF (NOT(exhibition_rec.start_datetime <= current_datetime
                  AND current_datetime <= exhibition_rec.end_datetime
                  AND current_datetime <= GREATEST(exhibition_rec.default_end_datetime, exhibition_rec.max_extend_datetime))
             ) THEN
        error_code := 'PERIOD_ERROR';
      ELSEIF exhibition_rec.hummer_flag <> 0 THEN
        error_code := 'HUMMER_ERROR';
      ELSEIF price < 0 THEN
        error_code := 'PRICE_FORMAT_ERROR';
      ELSEIF price <> 0 AND ext_bid_min_useFlag = 1 AND price < exhibition_rec.lowest_bid_price THEN
        error_code := 'LOWER_THAN_LOWEST_BID_PRICE_ERROR';
      ELSEIF quantity < exhibition_rec.lowest_bid_quantity THEN
        error_code := 'LOWER_THAN_LOWEST_BID_QUANTITY_ERROR';
      ELSEIF quantity > exhibition_rec.quantity THEN
        error_code := 'UPPER_THAN_QUANTITY_ERROR';
      ELSE
        after_bid_end_datetime := exhibition_rec.end_datetime;

        temp_sql := 'SELECT bid_no
                          , bid_price
                       FROM t_bid
                      WHERE exhibition_item_no = $1
                        AND member_no = $2';
        EXECUTE temp_sql
           INTO last_bid_no, last_bid_price
          USING exhibition_item_no, user_rec.member_no;
        IF last_bid_no IS NOT NULL THEN
            temp_sql := 'WITH BID_TBL AS (
                           UPDATE t_bid
                              SET bid_price = $1
                                , bid_quantity = $2
                                , bid_user_no = $3
                                , update_user_no = $3
                                , update_datetime = CASE WHEN bid_price = $1 AND bid_quantity = $2
                                                         THEN update_datetime
                                                         ELSE now()
                                                     END
                            WHERE bid_no = $4
                            RETURNING tenant_no
                                    , exhibition_item_no
                                    , member_no
                                    , bid_no
                                    , bid_price
                                    , bid_quantity
                                    , bid_user_no
                                    , create_user_no
                                    , create_datetime
                                    , update_user_no
                                    , update_datetime
                         )
                   SELECT *
                     FROM BID_TBL';
            EXECUTE temp_sql
              INTO regist_bid_rec
              USING price
                  , quantity
                  , in_bid_user_no
                  , last_bid_no;
        ELSE
            temp_sql := 'WITH BID_TBL AS (
                           INSERT INTO t_bid
                                     (
                                       tenant_no
                                     , exhibition_item_no
                                     , member_no
                                     , bid_price
                                     , bid_quantity
                                     , bid_user_no
                                     , create_user_no
                                     , create_datetime
                                     , update_user_no
                                     , update_datetime
                                     )
                                VALUES
                                     (
                                       $1
                                     , $2
                                     , $3
                                     , $4
                                     , $5
                                     , $6
                                     , $7
                                     , now()
                                     , $8
                                     , now()
                                     )
                           RETURNING
                             tenant_no
                           , exhibition_item_no
                           , member_no
                           , bid_price
                           , bid_quantity
                           , bid_user_no
                           , create_user_no
                           , update_user_no
                         )
                         SELECT *
                           FROM BID_TBL';
            EXECUTE temp_sql
              INTO regist_bid_rec
              USING
                exhibition_rec.tenant_no
              , exhibition_item_no
              , user_rec.member_no
              , price
              , quantity
              , in_bid_user_no
              , in_bid_user_no
              , in_bid_user_no;
        END IF;
          temp_sql := 'INSERT INTO t_bid_history
                                  (
                                    tenant_no
                                  , exhibition_item_no
                                  , member_no
                                  , bid_price
                                  , bid_quantity
                                  , bid_user_no
                                  , bid_user_name
                                  , bid_nickname
                                  , create_user_no
                                  , create_datetime
                                  )
                             VALUES
                                  (
                                    $1
                                  , $2
                                  , $3
                                  , $4
                                  , $5
                                  , $6
                                  , $7
                                  , $8
                                  , $9
                                  , now()
                                  )';
          EXECUTE temp_sql
            USING
              exhibition_rec.tenant_no
            , exhibition_item_no
            , user_rec.member_no
            , price
            , quantity
            , in_bid_user_no
            , in_user_name
            , user_rec.nickname
            , in_bid_user_no;
            temp_sql := 'UPDATE t_exhibition_item
                            SET bid_count = COALESCE(bid_count, 0) + 1
                          WHERE exhibition_item_no = $1';
            EXECUTE temp_sql
              USING
                exhibition_item_no;
            temp_sql := 'UPDATE t_exhibition_summary
                            SET bid_count = COALESCE(bid_count, 0) + 1
                          WHERE exhibition_no = $1';
            EXECUTE temp_sql
              USING
                exhibition_rec.exhibition_no;

          ------------------------------------------------------------------------------------
          -- 即決処理: 即決価格を超える入札があった場合
          ------------------------------------------------------------------------------------
          IF ext_deal_flag = 1 AND exhibition_rec.deal_bid_price IS NOT NULL AND exhibition_rec.deal_bid_price <= price THEN
            temp_sql := 'UPDATE t_exhibition_item
                            SET current_price = $1
                              , bid_success_member_no = $2
                              , top_member_no = $2
                              , end_datetime = now()
                              , default_end_datetime = now()
                          WHERE exhibition_item_no = $3';
            EXECUTE temp_sql
              USING
              price,
              user_rec.member_no,
              exhibition_item_no;
          END IF;
        END IF;
    END IF;

    IF error_code = '' THEN
        temp_text := '{
          "category_id": "' || exhibition_rec.category_id || '",
          "exhibition_item_no":"' || exhibition_item_no || '",
          "auction_classification":"' || ext_auction_classification || '",
          "status": ' || CASE WHEN regist_bid_rec.bid_price = 0 THEN 0 ELSE 1 END || ',
          "bid_price":"' || COALESCE(regist_bid_rec.bid_price::character varying, '') || '",
          "bid_quantity":"' || COALESCE(regist_bid_rec.bid_quantity::character varying, '') || '",
          "extending":' || COALESCE(after_bid_end_datetime > exhibition_rec.end_datetime, FALSE)::character varying || ',
          "remaining_seconds":' || ((DATE_PART('day', after_bid_end_datetime - now()) * 24 + DATE_PART('hour', after_bid_end_datetime - now())) * 60 + DATE_PART('minute', after_bid_end_datetime - now())) * 60 + DATE_PART('second', after_bid_end_datetime - now()) || '
        }';
    ELSE
      temp_text := '{"exhibition_item_no":"' || COALESCE(exhibition_item_no::character varying, temp_exhibition_item_no, '') || '", "errorMessage":"' || error_code || '"}';
      error_count := error_count + 1;
      status := 200;
    END IF;
    IF result_text <> '' THEN
      result_text := COALESCE(result_text, '') || ',';
    END IF;
    result_text := COALESCE(result_text, '') || temp_text;
  END LOOP;
  RAISE NOTICE 'BID LOOP終了';
  temp_text := format('{"result":{"statusCode":%s, "errorCount":%I, "bidList": %s, "topChangeCount":%I, "topChangeList": %s, "topAndSecondList": %s}}',
                 status, error_count, '[' || COALESCE(result_text, '') || ']', 0, '[]', '[]');
  result_data := temp_text::json;
  RAISE NOTICE 'BID正常終了==%' , temp_text::json;
  RETURN result_data;
END;
$BODY$;
