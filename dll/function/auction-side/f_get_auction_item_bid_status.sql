CREATE OR REPLACE FUNCTION public.f_get_auction_item_bid_status (
    in_exhibition_item_no bigint[],
    in_tenant_no bigint,
    in_member_no bigint
)
RETURNS TABLE(
  exhibition_item_no bigint,
  result jsonb
)

LANGUAGE plpgsql


COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 入札会商品取得
----------------------------------------------------------------------------------------------------

BEGIN
  RETURN QUERY
  WITH exhibition_info AS (
    SELECT TEI.exhibition_item_no,
           (CASE
             WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, FE.end_datetime) THEN TEI.end_datetime
             ELSE COALESCE(TEI.default_end_datetime, FE.end_datetime)
           END) AS end_datetime,
           (TEI.end_datetime > COALESCE(TEI.default_end_datetime, FE.end_datetime)) AS extending,
           CASE WHEN FE.pitch_option = 1 THEN FE.pitch_width ELSE COALESCE(NULLIF(AUTO_PITCH_CURRENT_PRICE.value1, '')::numeric, 0) END AS pitch_width,
           CASE WHEN FE.pitch_option = 1 THEN FE.pitch_width ELSE COALESCE(NULLIF(AUTO_PITCH_BID_PRICE.value1, '')::numeric, 0) END AS bid_price_pitch_width
    FROM t_exhibition_item TEI
    JOIN t_exhibition FE
      ON FE.exhibition_no = TEI.exhibition_no
    LEFT JOIN t_bid TB
      ON TB.exhibition_item_no = TEI.exhibition_item_no
     AND TB.tenant_no = TEI.tenant_no
     AND TB.member_no = in_member_no

    LEFT JOIN (
      SELECT MAX(value1) AS value1, MAX(value2) AS value2, value3, value4
        FROM m_constant_localized
       WHERE constant_no IN (SELECT constant_no FROM m_constant WHERE key_string='PITCH_FOLLOW_BID_PRICE')
         AND language_code = 'ja'
       GROUP BY value3, value4
    ) AUTO_PITCH_CURRENT_PRICE ON (AUTO_PITCH_CURRENT_PRICE.value3 IS NULL OR LENGTH(AUTO_PITCH_CURRENT_PRICE.value3) = 0 OR (f_isnumeric(AUTO_PITCH_CURRENT_PRICE.value3) AND COALESCE(TEI.current_price, TEI.lowest_bid_price) >= COALESCE(NULLIF(AUTO_PITCH_CURRENT_PRICE.value3, '')::numeric, 0)))
                AND (AUTO_PITCH_CURRENT_PRICE.value4 IS NULL OR LENGTH(AUTO_PITCH_CURRENT_PRICE.value4) = 0 OR (f_isnumeric(AUTO_PITCH_CURRENT_PRICE.value4) AND COALESCE(TEI.current_price, TEI.lowest_bid_price) < COALESCE(NULLIF(AUTO_PITCH_CURRENT_PRICE.value4, '')::numeric, 0)))

    LEFT JOIN (
      SELECT MAX(value1) AS value1, MAX(value2) AS value2, value3, value4
        FROM m_constant_localized
       WHERE constant_no IN (SELECT constant_no FROM m_constant WHERE key_string='PITCH_FOLLOW_BID_PRICE')
         AND language_code = 'ja'
       GROUP BY value3, value4
    ) AUTO_PITCH_BID_PRICE ON (AUTO_PITCH_BID_PRICE.value3 IS NULL OR LENGTH(AUTO_PITCH_BID_PRICE.value3) = 0 OR (f_isnumeric(AUTO_PITCH_BID_PRICE.value3) AND COALESCE(TB.bid_price, TEI.lowest_bid_price) >= COALESCE(NULLIF(AUTO_PITCH_BID_PRICE.value3, '')::numeric, 0)))
                AND (AUTO_PITCH_BID_PRICE.value4 IS NULL OR LENGTH(AUTO_PITCH_BID_PRICE.value4) = 0 OR (f_isnumeric(AUTO_PITCH_BID_PRICE.value4) AND COALESCE(TB.bid_price, TEI.lowest_bid_price) < COALESCE(NULLIF(AUTO_PITCH_BID_PRICE.value4, '')::numeric, 0)))

    WHERE in_exhibition_item_no IS NULL OR TEI.exhibition_item_no = ANY(in_exhibition_item_no)
  )
  SELECT  TEI.exhibition_item_no,
          json_build_object(
            'is_recommending', (TI.status = 2 AND now() BETWEEN TI.recommend_start_datetime AND TI.recommend_end_datetime),
            'sold_out', TEI.hummer_flag = 1,
            'start_datetime', TEI.start_datetime,
            'end_datetime', EI.end_datetime,
            'attention_info', json_build_object(
                      'favorited_count', TEI.favorite_count,
                      'is_favorited', TEIF.favorite_no IS NOT NULL,
                      'view_count', TEI.view_count,
                      'bid_count', TBH.cnt,
                      'nyusatsu_seigen',
                        CASE (FE.exhibition_classification_info->>'bidLimit')::int
                          WHEN 0 THEN 'free'
                          WHEN 1 THEN 'higher-than-previous-own-bid'
                          WHEN 2 THEN 'higher-than-current-price'
                          ELSE 'unknown'
                        END, -- 入札額制限: 0:自由、1:前回自社入札より高い、2：現在価格より高い
                      'isCancelBidAllowed', FE.exhibition_classification_info-> 'bidCancelFlag' = '1', -- 入札取消可否: 0: 不可, 1: 入力可
                      'saiTeiRakuSatsuKakakuShiyouUmu', FE.exhibition_classification_info-> 'bidCommitMinUseFlag' = '1', -- 最低落札価格使用有無: 0: なし, 1: あり
                      'isFastPriceAuction', FE.exhibition_classification_info-> 'bidCommitClassification' = '1', -- 落札価格決定区分: 1: ファーストプライス, 2: セカンドプライス
                      'bidCommitMinDisplayFlag', FE.exhibition_classification_info-> 'bidCommitMinDisplayFlag' -- 最低落札表示有無フラグ: 0: なし, 1: あり
                    ),
            'bid_status', json_build_object(
              'automatic_bidding', FE.exhibition_classification_info->'bidCommitClassification' = '2',
              'can_bid', (FE.status = 0 AND FE.start_datetime <= now() AND now() <= EI.end_datetime
                         AND (FE.max_extend_datetime IS NULL OR now() <= GREATEST(TEI.default_end_datetime, FE.max_extend_datetime))
                         )
                         AND TEI.cancel_flag = 0,
              'status', CASE WHEN TB.bid_price IS NULL THEN 0 --未入札
                             WHEN TEI.top_member_no = in_member_no AND TEI.lowest_bid_accept_price <= TB.bid_price AND TEI.cancel_flag = 0 THEN 3 --落札権あり
                             ELSE 1 END, --入札済み
              'current_price', COALESCE(TEI.current_price, TEI.lowest_bid_price),
              'tax_rate', CASE WHEN TER.exhibition_no IS NULL THEN COALESCE(NULLIF(TAX_NOW.value1, '')::numeric, 0) ELSE COALESCE(TER.tax_rate, 0) END, -- 確定済みの場合は入札した時点の税率を使う、それ以外は現時点の税率
              'quantity', TEI.quantity,
              'lowest_bid_quantity', TEI.lowest_bid_quantity,
              'lowest_bid_price', TEI.lowest_bid_price,
              'top_price', COALESCE(TEI.top_price, 0),
              'top_member_nickname', COALESCE(TOP_MEM.free_field->>'nickname', ''), --TOP Member nickname
              'is_top_member', TEI.top_member_no = in_member_no AND TEI.lowest_bid_accept_price <= TEI.current_price AND TEI.cancel_flag = 0, --TOP
              'is_second_member', TEI.top_member_no <> in_member_no AND TEI.lowest_bid_accept_price <= TB.bid_price AND (TB.bid_price + COALESCE(EI.bid_price_pitch_width, 0)) >= TEI.current_price AND TEI.cancel_flag = 0, --2nd
              'is_exceeding_lowest_price', TEI.top_member_no IS NOT NULL AND TEI.lowest_bid_accept_price <= TEI.current_price AND TEI.cancel_flag = 0, --最低落札価格超え
              'is_not_exceeding_lowest_price', TEI.top_member_no IS NOT NULL AND TEI.current_price <> TEI.lowest_bid_price AND TEI.lowest_bid_accept_price > TEI.current_price AND TEI.cancel_flag = 0, --最低落札価格未達
              'is_more_little', TEI.top_member_no IS NOT NULL AND TEI.lowest_bid_accept_price > COALESCE(TEI.top_price, 0) AND TEI.lowest_bid_accept_price <= (COALESCE(TEI.top_price, 0) + COALESCE(FE.more_little_judge_pitch, 0)*COALESCE(EI.pitch_width, 0)) AND TEI.cancel_flag = 0, --あと少し
              'is_cancel', TEI.cancel_flag = 1, --出品停止
              'bid_price', TB.bid_price,
              'bid_quantity', TB.bid_quantity,
              'pitch_width', EI.pitch_width,
              'pitch_button_1', EI.pitch_width,
              'pitch_button_2', EI.pitch_width * 10,
              'pitch_button_3', EI.pitch_width * 50,
              'pitch_option', FE.pitch_option,
              'started', now() > TEI.start_datetime,
              'extending', EI.extending,
              'start_datetime', TEI.start_datetime,
              'end_datetime', EI.end_datetime,
              'remaining_seconds', ((DATE_PART('day', EI.end_datetime - now()) * 24 + DATE_PART('hour',EI.end_datetime - now())) * 60 + DATE_PART('minute', EI.end_datetime - now())) * 60 + DATE_PART('second', EI.end_datetime - now()),
              'deal_bid_price', TEI.deal_bid_price
            )
         )::jsonb
    FROM t_exhibition_item TEI
    JOIN t_exhibition FE
      ON FE.exhibition_no = TEI.exhibition_no
    JOIN exhibition_info EI
      ON EI.exhibition_item_no = TEI.exhibition_item_no
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
     AND TLD.tenant_no = in_tenant_no
     AND TLD.order_no = 1
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
     AND TI.tenant_no = in_tenant_no
     AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    LEFT JOIN t_exhibition_item_favorite TEIF
      ON TEIF.exhibition_item_no = TEI.exhibition_item_no
     AND TEIF.tenant_no = TEI.tenant_no
     AND TEIF.member_no = in_member_no
    LEFT JOIN t_bid TB
      ON TB.exhibition_item_no = TEI.exhibition_item_no
     AND TB.tenant_no = TEI.tenant_no
     AND TB.member_no = in_member_no
    LEFT JOIN (
      SELECT TBH.exhibition_item_no,
            COUNT(*) as cnt
        FROM t_bid_history TBH
        LEFT JOIN t_bid TB1
               ON TB1.tenant_no = TBH.tenant_no
              AND TB1.exhibition_item_no = TBH.exhibition_item_no
              AND TB1.member_no = TBH.member_no
        WHERE TB1.bid_price IS NOT NULL
          AND TB1.bid_price <> 0
        GROUP BY TBH.exhibition_item_no

    ) TBH ON TBH.exhibition_item_no = TEI.exhibition_item_no
    LEFT JOIN m_member TOP_MEM
      ON TOP_MEM.member_no = TEI.top_member_no
     AND TOP_MEM.delete_flag = 0
    LEFT OUTER JOIN (
      SELECT mc.tenant_no
          , mcl.value1
          , mcl.value2
        FROM m_constant mc
        INNER JOIN m_constant_localized mcl ON mcl.constant_no = mc.constant_no
      WHERE mcl.language_code = 'ja'
        AND mc.key_string = 'TAX_RATE'
        AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
    ) TAX_NOW
      ON TAX_NOW.tenant_no = TEI.tenant_no
    LEFT JOIN t_exhibition_result TER
      ON TER.exhibition_no = TEI.exhibition_no
      AND TER.exhibition_item_no = TEI.exhibition_item_no
    WHERE in_exhibition_item_no IS NULL OR TEI.exhibition_item_no = ANY (in_exhibition_item_no)
    ;
END;

$BODY$;
