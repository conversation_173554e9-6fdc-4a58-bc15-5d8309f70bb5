CREATE OR REPLACE FUNCTION public.f_batch_exhibition_auction_end ()
RETURNS TABLE(
    tenant_no bigint,
    exhibition_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 終了した開催回ステータス更新
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH update_exhibition_status AS (
    UPDATE t_exhibition TE
       SET status = 1,
           update_datetime = now()
      FROM t_exhibition_item TEI
     WHERE (TE.delete_flag IS NULL OR TE.delete_flag = 0)
      --  AND TE.status = 0
       AND now() > TE.end_datetime
       AND now() > (CASE
                      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                    END)
       AND (
       SELECT COUNT(*)
         FROM t_exhibition_item TEI
        WHERE TEI.exhibition_no = TE.exhibition_no
          AND TEI.tenant_no = TE.tenant_no
          AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
          AND TEI.hummer_flag = 0
     ) = 0
    RETURNING
      TE.exhibition_no
  )
  UPDATE t_exhibition_summary TES
     SET contract_count = (
           SELECT count(DISTINCT lot_no)
             FROM t_exhibition_result TER
            WHERE TER.hummer_flag = 1
              AND TER.exhibition_no = TES.exhibition_no
         ),
         update_datetime = now()
   WHERE TES.exhibition_no IN (
     SELECT UES.exhibition_no FROM update_exhibition_status UES
   )
  RETURNING
    TES.tenant_no,
    TES.exhibition_no;

END;

$BODY$;
