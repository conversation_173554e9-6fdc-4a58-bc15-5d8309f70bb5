# GMO SaaS向けWebオークションシステム

このドキュメントでは、GMOのSaaS向けWebオークションシステムの概要、セットアップ手順、デプロイガイドラインを説明します。

## 目次

- [GMO SaaS向けWebオークションシステム](#gmo-saas向けwebオークションシステム)
  - [目次](#目次)
  - [概要](#概要)
    - [実行環境および開発ツール](#実行環境および開発ツール)
  - [ローカル環境設定](#ローカル環境設定)
      - [フロントエンドデプロイ](#フロントエンドデプロイ)
      - [Terraformデプロイ](#terraformデプロイ)
  - [デプロイ手順](#デプロイ手順)
    - [ステップ1：ソースコードのコンパイルとS3へのアップロード](#ステップ1ソースコードのコンパイルとs3へのアップロード)
      - [開発環境](#開発環境)
      - [本番環境](#本番環境)
    - [ステップ2：API Gatewayのデプロイ](#ステップ2api-gatewayのデプロイ)
      - [管理サイト](#管理サイト)
      - [会員サイト](#会員サイト)
  - [システム設計](#システム設計)
  - [本番環境構築ガイド](#本番環境構築ガイド)
  - [追加情報](#追加情報)
    - [関数クエリの実行](#関数クエリの実行)
    - [SlackでのGitHubリポジトリ通知設定](#slackでのgithubリポジトリ通知設定)
    - [問い合わせ機能のテスト手順](#問い合わせ機能のテスト手順)
    - [VSCode環境設定](#vscode環境設定)

## 概要

GMO SaaS Webオークションシステムは、オンラインオークションを実施するためのクラウドベースのプラットフォームで、管理者および会員向けの機能をサポートしています。

### 実行環境および開発ツール

- **Node.js**: バージョン 20.x
- **Terraform**: バージョン 1.9.8

## ローカル環境設定

以下の手順に従って、ローカル環境をセットアップしてください。

<details>
<summary>1. AWS アカウントの SSO 設定</summary>

このプロジェクトでは、AWS SSO（シングルサインオン）を使用して `saas-dev` および `saas-prod` プロファイルにログインします。

まず、以下のコマンドで SSO 設定を行ってください。

```bash
aws configure sso
```

コマンド実行後、以下の質問に順番に回答します。

1. **SSO session name**（任意の名前を入力）
   例：`my-session`

2. **SSO start URL**

   ```
   https://d-956762e44c.awsapps.com/start/#
   ```

3. **SSO region**

   ```
   ap-northeast-1
   ```

4. **SSO registration scopes**
   → 何も入力せず、そのまま `Enter`

5. ブラウザが自動で開くので、サインイン後に「確認」ボタンを押してください。

6. ターミナルに戻ると、利用可能なアカウントとロールの一覧が表示されます。
   `gmoms_auction-dev-saas` を選択してください。

7. **CLI default client Region**
   → 何も入力せず、そのまま `Enter`

8. **CLI default output format**
   → 何も入力せず、そのまま `Enter`

9. **CLI profile name**

   ```
   saas-dev
   ```

上記の手順で `saas-dev` プロファイルが作成され、AWS CLI を通して認証済みアクセスが可能になります。

> 同様の手順で `saas-prod` プロファイルも設定できます（ステップ9で `saas-prod` と入力）。

</details>

<details>
<summary>2. Terraformのインストール</summary>

バージョン管理ツール`tfenv`を使用して、Terraform 1.9.8をインストールします。

```bash
tfenv install 1.9.8
tfenv use 1.9.8
```

Terraformのバージョンを確認します。

```bash
terraform -v
```

</details>

<details>
<summary>3. デプロイ確認</summary>

上記の手順1および2を完了後、以下のコマンドでデプロイを確認します。

#### フロントエンドデプロイ

```bash
npm run deploy-dev
```

#### Terraformデプロイ

```bash
cd infrastructure/$ENVIRONMENT
terraform init  # 初回のみ
terraform apply
```

> **注意**: 作成または変更する設定が意図したものであることを確認し、`yes`を入力してください。

</details>

## デプロイ手順

クライアントアプリケーションをデプロイするには、以下のステップに従ってください。

### ステップ1：ソースコードのコンパイルとS3へのアップロード

#### 開発環境

**管理サイト**（`admin-side`ディレクトリ内）:

```bash
npm run deploy-dev
```

**会員サイト**（`auction-side`ディレクトリ内）:

```bash
npm run deploy-dev
```

#### 本番環境

**管理サイト**（`admin-side`ディレクトリ内）:

```bash
npm run deploy-prod
```

**会員サイト**（`auction-side`ディレクトリ内）:

```bash
npm run deploy-prod
```

### ステップ2：API Gatewayのデプロイ

#### 管理サイト

```bash
cd infrastructure/$ENVIRONMENT
terraform apply -target module.common.module.admin-side.module.gateway-resource.module.$API_NAME
terraform apply -target module.common.module.admin-side.aws_api_gateway_deployment.gateway_deploy -target module.common.module.admin-side.aws_api_gateway_stage.gateway_stage -var="manual_deploy=true"
```

#### 会員サイト

```bash
cd infrastructure/$ENVIRONMENT
terraform apply -target module.common.module.auction-side.module.gateway-resource.module.$API_NAME
terraform apply -target module.common.module.auction-side.aws_api_gateway_deployment.gateway_deploy -target module.common.module.auction-side.aws_api_gateway_stage.gateway_stage -var="manual_deploy=true"
```

**ヒント**: すべての変更を一括で適用する場合、以下の統合コマンドを使用できます。

```bash
cd infrastructure/$ENVIRONMENT
terraform apply -target module.common.module.admin-side.module.gateway-resource.module.$API_NAME \
                -target module.common.module.auction-side.module.gateway-resource.module.$API_NAME
```

## システム設計

<details>
<summary>オークション仕様（概要）</summary>

以下の表は、オークション情報の主なキーおよびその意味をまとめたものです。

| 項目                   | 説明                                                                    | テーブル/フィールド                                         |
| ---------------------- | ----------------------------------------------------------------------- | ----------------------------------------------------------- |
| オークション方式       | 1: せり上げ <br> 2: 封印入札                                            | `exhibition_classification_info.auctionClassification`      |
| 落札価格決定区分       | 1: ファーストプライス <br> 2: セカンドプライス                          | `exhibition_classification_info.bidCommitClassification`    |
| 落札結果確定済         | 0: 未入札 <br> 1: 入札あり <br> 2: 落札(SOLD OUT) <br> 3: 流札          | `exhibition_item_no.hummer_flag`                            |
| ピッチ区分             | 1: 指定単位 (SaaSでは固定1) <br> 2: 入札価格帯 (例: 価格により単位変動) | `pitch_option`                                              |
| 公開区分               | 1: 一般公開 <br> 2: 特定の参加者                                        | `exhibition_classification_info.openClassification`         |
| 入札回数制限           | 0: 制限なし <br> 1: 1回のみ                                             | `exhibition_classification_info.bidCountLimit`              |
| 入札額制限             | （0:自由、1:前回自社入札より高い、2：現在価格より高い）  | `exhibition_classification_info.bidLimit`   |
| 最低入札価格使用有無   | 0: なし <br> 1: あり                                                    | `exhibition_classification_info.bidMinUseFlag`              |
| 最低落札未達入力可否   | 0: 入力不可 <br> 1: 入力可                                              | `exhibition_classification_info.bidShortFlag`               |
| 最低落札価格使用有無   | 0: なし <br> 1: あり                                                    | `exhibition_classification_info.bidCommitMinUseFlag`        |
| 最低落札表示有無フラグ | 0: なし <br> 1: あり                                                    | `exhibition_classification_info.bidCommitMinDisplayFlag`    |
| 入札取消可否           | 0: 不可 <br> 1: 入力可                                                  | `exhibition_classification_info.bidCancelFlag`              |
| 即決有無               | 0: なし <br> 1: あり                                                    | `exhibition_classification_info.dealFlag`                   |
| 落札者決定区分         | 1: 自動 <br> 2: 手動設定                                                | `exhibition_classification_info.bidderCommitClassification` |
| 最低落札未達表示有無   | 0: なし <br> 1: あり                                                    | `exhibition_classification_info.bidShortDisplayFlag`        |
| 現在価格表示有無       | 0: なし <br> 1: あり                                                    | `exhibition_classification_info.priceDisplayFlag`           |
| リスク係数有無         | 0: なし <br> 1: あり                                                    | `exhibition_classification_info.riskAdjustmentFlag`         |
| 延長有無               | 0: なし <br> 1: あり                                                    | `exhibition_classification_info.extendFlag`                 |
| 入札件数表示有無       | 0: なし <br> 1: あり                                                    | `exhibition_classification_info.bidCountDisplayFlag`        |
| TOP時引き下げ          | 0: なし <br> 1: あり                                                    | `exhibition_classification_info.topReductionFlag`           |

</details>

## 本番環境構築ガイド

本番環境の設定方法については、以下のリンクを参照してください。

- [本番環境構築ガイド](./ProductionSetupGuide.md)

## 追加情報

### 関数クエリの実行

PgAdminを使用して関数クエリを実行します。

```bash
SELECT * FROM public.f_get_bid_history(1, 'ja', '{38}');
```

### SlackでのGitHubリポジトリ通知設定

詳細は以下のリンクを参照してください。

- [GitHub-Slack通知設定](https://zenn.dev/mixi/articles/github-slack-notification-disabling-thread)

### 問い合わせ機能のテスト手順

問い合わせ機能のテストを行う場合は、以下の手順に従ってください。

1. 管理者アカウントでログインします。
2. `EMAIL_INQUIRY_REQUEST_FOR_ADMIN`定数を見つけ、受信したいメールアドレスに変更します。
3. テスト完了後、元の設定に戻します。

### VSCode環境設定

詳細は以下のリンクを参照してください。

- [ローカル環境設定](./docs/settings/LOCAL-SETTING.md)
