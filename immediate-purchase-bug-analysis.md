# Immediate Purchase (Deal Price) Bug Analysis & Solution Plan

## Root Cause Analysis

### Problem Summary
When a bid exceeds the deal price (40,000 yen < 43,333 yen bid), the auction should end immediately but currently:
1. ✅ `t_exhibition_item.hummer_flag` is correctly set to `1` (winning state)
2. ✅ Winning bid details are correctly updated in `t_exhibition_item`
3. ❌ **Exhibition status remains active (status = 0) instead of ending (status = 1)**
4. ❌ Subsequent bids are blocked with "HUMMER_ERROR" but auction appears active

### Root Cause Identified

**The immediate purchase logic in `f_regist_bid.sql` only updates the item-level `hummer_flag` but does NOT check if the exhibition should be ended immediately.**

#### Current Logic Flow:
1. **Item Level**: `f_regist_bid.sql` lines 848-860 correctly set `hummer_flag = 1` for immediate purchase
2. **Exhibition Level**: **MISSING** - No logic to check if all items in exhibition are finished and end exhibition immediately
3. **Batch Dependency**: Exhibition ending only happens via batch process `f_batch_exhibition_auction_end.sql` which checks:
   - Time-based ending: `now() > TE.end_datetime`
   - All items finished: `COUNT(*) WHERE hummer_flag = 0) = 0`

#### Key Issue:
The batch process requires **BOTH** time-based AND completion-based conditions, but immediate purchase should end the exhibition **regardless of time** when all items are complete.

### Analysis of Batch Logic

From `f_batch_exhibition_auction_end.sql` lines 22-42:
```sql
UPDATE t_exhibition TE
   SET status = 1, update_datetime = now()
  FROM t_exhibition_item TEI
 WHERE (TE.delete_flag IS NULL OR TE.delete_flag = 0)
   AND TE.status = 0
   AND now() > TE.end_datetime  -- ❌ TIME CONSTRAINT BLOCKS IMMEDIATE ENDING
   AND now() > (CASE WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                     THEN TEI.end_datetime ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime) END)
   AND (SELECT COUNT(*) FROM t_exhibition_item TEI
        WHERE TEI.exhibition_no = TE.exhibition_no AND TEI.tenant_no = TE.tenant_no
          AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0) AND TEI.hummer_flag = 0) = 0
```

**The time constraints prevent immediate exhibition ending even when all items are complete.**

## Solution Plan

### Phase 1: Add Immediate Exhibition Ending Logic

#### 1.1 Create Helper Function for Exhibition Completion Check
**File**: `dll/function/auction-side/f_check_exhibition_completion.sql`

Create a reusable function to check if an exhibition should be ended immediately:
- Check if all items in exhibition have `hummer_flag != 0`
- Return exhibition completion status
- Handle multi-item exhibitions properly

#### 1.2 Modify f_regist_bid.sql - Add Immediate Exhibition Ending
**File**: `dll/function/auction-side/f_regist_bid.sql`
**Location**: After line 860 (immediate purchase logic)

Add logic to:
1. Check if current exhibition has all items completed after immediate purchase
2. If yes, immediately update `t_exhibition.status = 1`
3. Update `t_exhibition_summary.contract_count`
4. Ensure proper transaction handling

#### 1.3 Modify f_regist_bid_sealed.sql - Add Same Logic
**File**: `dll/function/auction-side/f_regist_bid_sealed.sql`
**Location**: After line 382 (immediate purchase logic)

Apply same immediate exhibition ending logic for sealed bid auctions.

### Phase 2: Enhance Batch Process for Immediate Purchase Cases

#### 2.1 Modify Exhibition Ending Batch Logic
**File**: `dll/function/batch/f_batch_exhibition_auction_end.sql`

Update the WHERE clause to handle immediate purchase cases:
```sql
AND (
  now() > TE.end_datetime  -- Normal time-based ending
  OR (  -- OR immediate ending when all items complete
    (SELECT COUNT(*) FROM t_exhibition_item TEI
     WHERE TEI.exhibition_no = TE.exhibition_no AND TEI.tenant_no = TE.tenant_no
       AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0) AND TEI.hummer_flag = 0) = 0
  )
)
```

### Phase 3: Testing Strategy

#### 3.1 Unit Testing
1. **Single Item Exhibition with Deal Price**
   - Bid below deal price → auction continues
   - Bid at deal price → immediate ending
   - Bid above deal price → immediate ending

2. **Multi-Item Exhibition with Deal Price**
   - One item reaches deal price, others active → auction continues
   - All items reach deal price → immediate ending
   - Mixed completion states → proper handling

3. **Edge Cases**
   - Concurrent bids on multiple items
   - Deal price exactly equal to bid
   - Exhibition with no deal price set

#### 3.2 Integration Testing
1. **End-to-End Flow**
   - Lambda → f_regist_bid_combine → f_regist_bid → Exhibition ending
   - Verify WebSocket notifications
   - Verify email notifications (if enabled)

2. **Batch Process Compatibility**
   - Ensure batch processes still work for time-based endings
   - Verify no duplicate exhibition ending
   - Test batch performance with immediate-ended exhibitions

### Phase 4: Implementation Order

#### Step 1: Create Helper Function
```sql
-- f_check_exhibition_completion.sql
-- Returns: exhibition_no, should_end_immediately, total_items, completed_items
```

#### Step 2: Modify f_regist_bid.sql
Add immediate exhibition ending logic after immediate purchase update.

#### Step 3: Modify f_regist_bid_sealed.sql
Apply same logic for sealed bid auctions.

#### Step 4: Update Batch Process
Modify f_batch_exhibition_auction_end.sql to handle immediate completion cases.

#### Step 5: Testing & Validation
- Test with provided scenario (deal_price: 40,000, bid: 43,333)
- Verify no "HUMMER_ERROR" on properly ended exhibitions
- Confirm exhibition status changes to "END" immediately

### Expected Behavior After Fix

#### Immediate Purchase Scenario:
1. **Bid Submission**: 43,333 yen (> 40,000 deal price)
2. **Item Update**: `hummer_flag = 1`, winning details updated
3. **Exhibition Check**: All items in exhibition completed?
4. **If Yes**: `t_exhibition.status = 1` (END), `t_exhibition_summary` updated
5. **Result**: Auction shows as "ENDED", no further bids accepted
6. **No "HUMMER_ERROR"**: Proper exhibition ending prevents error state

#### Multi-Item Exhibition:
- Only ends when ALL items reach completion (hummer_flag != 0)
- Partial completion keeps exhibition active
- Proper handling of mixed auction types within same exhibition

This solution addresses the core issue while maintaining compatibility with existing batch processes and ensuring proper auction lifecycle management.

## Revised Implementation Plan (Leveraging Existing Batch Infrastructure)

### Technical Approach
Your approach is excellent! However, PostgreSQL functions cannot directly invoke Lambda functions. Instead, we'll:
1. **Manipulate timestamps** in PostgreSQL to make auctions appear "time-expired"
2. **Trigger batch processes** from the existing Lambda context after PostgreSQL execution
3. **Leverage existing infrastructure** for proper auction ending, email notifications, etc.

### Step 1: Modify f_regist_bid.sql - Add Timestamp Manipulation

**File**: `dll/function/auction-side/f_regist_bid.sql`
**Location**: Modify the immediate purchase logic block (lines 848-860)

```sql
-- Replace the existing immediate purchase logic with enhanced version
IF ext_deal_flag = 1 AND exhibition_rec.deal_bid_price IS NOT NULL AND exhibition_rec.deal_bid_price <= price THEN
  -- Original immediate purchase updates
  temp_sql := 'UPDATE t_exhibition_item
                  SET current_price = $1
                    , bid_success_member_no = $2
                    , top_member_no = $2
                    , hummer_flag = 1
                WHERE exhibition_item_no = $3';
  EXECUTE temp_sql
    USING
    price,
    after_top_data_rec.top_member_no,
    exhibition_item_no;

  -- NEW: Force immediate auction ending by manipulating timestamps
  RAISE NOTICE '即決価格到達 - タイムスタンプ操作による即座終了開始: exhibition_item_no=%, exhibition_no=%',
    exhibition_item_no, exhibition_rec.exhibition_no;

  -- Update item end times to now() to trigger batch ending
  temp_sql := 'UPDATE t_exhibition_item
                  SET end_datetime = now()
                    , default_end_datetime = now()
                WHERE exhibition_item_no = $1';
  EXECUTE temp_sql USING exhibition_item_no;

  -- Check if all items in exhibition are now complete
  DECLARE
    remaining_items_count integer := 0;
  BEGIN
    SELECT COUNT(*) INTO remaining_items_count
    FROM t_exhibition_item TEI
    WHERE TEI.exhibition_no = exhibition_rec.exhibition_no
      AND TEI.tenant_no = exhibition_rec.tenant_no
      AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
      AND TEI.hummer_flag = 0;

    -- If all items are complete, force exhibition end time
    IF remaining_items_count = 0 THEN
      RAISE NOTICE '全商品完了 - 開催回終了時刻を現在時刻に設定: exhibition_no=%', exhibition_rec.exhibition_no;

      temp_sql := 'UPDATE t_exhibition
                      SET end_datetime = now()
                    WHERE exhibition_no = $1
                      AND tenant_no = $2
                      AND status = 0';
      EXECUTE temp_sql USING exhibition_rec.exhibition_no, exhibition_rec.tenant_no;
    END IF;
  END;
END IF;
```

### Step 2: Modify Lambda Handler - Add Batch Process Triggering

**File**: `infrastructure/common/auction-side/gateway-resource/bid-items/source/index.js`
**Location**: After the PostgreSQL function call (around line 65)

```javascript
.then(data => {
  console.log(`data${JSON.stringify(data)}`)
  const result =
    data.length > 0 && Object.values(data[0]).length > 0
      ? Object.values(data[0])[0].result
      : {}

  // Check for bid validation errors
  const hasErrors = result.errorCount > 0 ||
    (result.bidList && result.bidList.some(bid => bid.errorMessage))
  console.log('👷‍♂️ log of hasErrors : ', hasErrors)

  // NEW: Check if any bids triggered immediate purchase (deal price reached)
  const hasImmediatePurchase = result.bidList && result.bidList.some(bid =>
    bid.auction_classification === '1' && // Ascending auction
    bid.status === 3 && // Successful bid status
    !bid.errorMessage
  )

  if (hasImmediatePurchase && !hasErrors) {
    console.log('🔨 Immediate purchase detected - triggering batch processes')

    // Trigger batch processes in sequence
    return Promise.resolve()
      .then(() => {
        console.log('📋 Triggering item auction end batch...')
        return Common.invokeLambdaEventType(
          process.env.ITEM_AUCTION_END_BATCH_LAMBDA_ARN, // Need to add this env var
          { triggeredBy: 'immediate_purchase', authorizer },
          { maxRetries: 0 }
        )
      })
      .then(() => {
        console.log('🏁 Triggering exhibition auction end batch...')
        return Common.invokeLambdaEventType(
          process.env.EXHIBITION_AUCTION_END_BATCH_LAMBDA_ARN, // Need to add this env var
          { triggeredBy: 'immediate_purchase', authorizer },
          { maxRetries: 0 }
        )
      })
      .then(() => {
        console.log('✅ Batch processes triggered successfully')
        return result // Continue with normal flow
      })
      .catch(batchError => {
        console.error('❌ Error triggering batch processes:', batchError)
        // Don't fail the main bid process, just log the error
        return result
      })
  }

  return result // Normal flow continues
})
```

### Step 3: Modify f_regist_bid_sealed.sql - Add Same Logic

**File**: `dll/function/auction-side/f_regist_bid_sealed.sql`
**Location**: After line 382 (immediate purchase logic)

Apply the same timestamp manipulation logic as in Step 1.

### Step 4: Add Environment Variables

**Files**: Terraform configuration files for the bid-items Lambda
- Add `ITEM_AUCTION_END_BATCH_LAMBDA_ARN` environment variable
- Add `EXHIBITION_AUCTION_END_BATCH_LAMBDA_ARN` environment variable

### Step 5: Batch Process Compatibility (Optional Enhancement)

The existing batch processes should work correctly with the timestamp manipulation approach since they already check for time-based endings. However, you could add logging to identify immediate purchase triggers:

**File**: `dll/function/batch/f_batch_exhibition_auction_end.sql`

```sql
-- Add logging to identify immediate purchase endings
RAISE NOTICE 'Exhibition ending detected: exhibition_no=%, end_datetime=%, current_time=%, immediate_ending=%',
  TE.exhibition_no,
  TE.end_datetime,
  now(),
  (TE.end_datetime <= now() AND TE.end_datetime > now() - interval '1 minute');
```

## Advantages of This Approach

### ✅ **Leverages Existing Infrastructure**
- Uses proven batch processes for auction ending
- Maintains email notification system
- Preserves all existing post-auction logic
- No risk of missing critical steps

### ✅ **Minimal Code Changes**
- Only modifies timestamp logic in PostgreSQL
- Adds batch triggering in existing Lambda
- No new database functions required
- Maintains transaction integrity

### ✅ **Proper Sequencing**
- Item-level updates happen first (PostgreSQL)
- Exhibition-level updates happen via batch (proper order)
- Email notifications triggered automatically
- All cleanup processes executed

## Testing Strategy

### Step 6: Testing Commands

#### Test Case 1: Single Item Immediate Purchase
```sql
-- Test immediate purchase
SELECT * FROM "f_regist_bid_combine"(15, 'Buff Account 2', '[{"exhibitionItemNo": 50, "bidPrice": 43333, "bidQuantity": 1}]');

-- Verify timestamps were updated
SELECT exhibition_item_no, end_datetime, default_end_datetime, hummer_flag
FROM t_exhibition_item WHERE exhibition_item_no = 50;

SELECT exhibition_no, end_datetime, status
FROM t_exhibition WHERE exhibition_no = (SELECT exhibition_no FROM t_exhibition_item WHERE exhibition_item_no = 50);

-- After batch processes run, verify final state
SELECT exhibition_no, status FROM t_exhibition WHERE exhibition_no = 1; -- Should be 1 (ended)
```

#### Test Case 2: Multi-Item Exhibition
```sql
-- Test partial completion (should NOT end exhibition immediately)
SELECT * FROM "f_regist_bid_combine"(15, 'Buff Account 2', '[{"exhibitionItemNo": 51, "bidPrice": 43333, "bidQuantity": 1}]');

-- Verify only item timestamps updated, exhibition end_datetime unchanged
SELECT exhibition_no, end_datetime FROM t_exhibition WHERE exhibition_no = 2; -- Should be future date

-- Test complete all items (should end exhibition)
SELECT * FROM "f_regist_bid_combine"(15, 'Buff Account 2', '[{"exhibitionItemNo": 52, "bidPrice": 53333, "bidQuantity": 1}]');

-- Verify exhibition end_datetime set to now()
SELECT exhibition_no, end_datetime FROM t_exhibition WHERE exhibition_no = 2; -- Should be ~now()
```

### Step 7: Validation Checklist

- [ ] **Timestamp Manipulation**: Item and exhibition end_datetime set to now() when appropriate
- [ ] **Batch Process Triggering**: Lambda successfully invokes batch functions after immediate purchase
- [ ] **Exhibition Status**: Status changes from 0 to 1 after batch processes complete
- [ ] **No HUMMER_ERROR**: Properly ended exhibitions don't cause subsequent bid errors
- [ ] **Multi-Item Handling**: Exhibition only ends when ALL items are complete
- [ ] **Email Notifications**: Existing email system works for immediate purchase endings
- [ ] **WebSocket Notifications**: Real-time updates work correctly
- [ ] **Performance**: Batch triggering doesn't significantly impact bid response time
- [ ] **Error Handling**: Batch process failures don't break main bid flow

## Implementation Questions & Considerations

### Q1: Environment Variables
**Question**: Do the batch Lambda ARNs need to be added to the bid-items Lambda environment?
**Answer**: Yes, you'll need to add these environment variables in your Terraform configuration.

### Q2: Batch Process Timing
**Question**: Should batch processes run synchronously or asynchronously?
**Answer**: **Asynchronously** is recommended to avoid impacting bid response time. The timestamp manipulation ensures the auction appears ended immediately.

### Q3: Error Handling
**Question**: What if batch processes fail?
**Answer**: The main bid should still succeed. Batch failures should be logged but not break the bidding flow. The timestamp manipulation provides a fallback - regular batch schedules will eventually process the "time-expired" auction.

### Q4: Race Conditions
**Question**: What about concurrent bids on the same exhibition?
**Answer**: PostgreSQL transaction isolation handles this. The first bid to reach deal price will set timestamps and trigger batches.

This approach provides **immediate visual feedback** (via timestamp manipulation) while ensuring **proper auction ending** (via existing batch infrastructure), giving you the best of both worlds!
