# Immediate Purchase (Deal Price) Bug Analysis & Solution Plan

## Root Cause Analysis

### Problem Summary
When a bid exceeds the deal price (40,000 yen < 43,333 yen bid), the auction should end immediately but currently:
1. ✅ `t_exhibition_item.hummer_flag` is correctly set to `1` (winning state)
2. ✅ Winning bid details are correctly updated in `t_exhibition_item`
3. ❌ **Exhibition status remains active (status = 0) instead of ending (status = 1)**
4. ❌ Subsequent bids are blocked with "HUMMER_ERROR" but auction appears active

### Root Cause Identified

**The immediate purchase logic in `f_regist_bid.sql` only updates the item-level `hummer_flag` but does NOT check if the exhibition should be ended immediately.**

#### Current Logic Flow:
1. **Item Level**: `f_regist_bid.sql` lines 848-860 correctly set `hummer_flag = 1` for immediate purchase
2. **Exhibition Level**: **MISSING** - No logic to check if all items in exhibition are finished and end exhibition immediately
3. **Batch Dependency**: Exhibition ending only happens via batch process `f_batch_exhibition_auction_end.sql` which checks:
   - Time-based ending: `now() > TE.end_datetime`
   - All items finished: `COUNT(*) WHERE hummer_flag = 0) = 0`

#### Key Issue:
The batch process requires **BOTH** time-based AND completion-based conditions, but immediate purchase should end the exhibition **regardless of time** when all items are complete.

### Analysis of Batch Logic

From `f_batch_exhibition_auction_end.sql` lines 22-42:
```sql
UPDATE t_exhibition TE
   SET status = 1, update_datetime = now()
  FROM t_exhibition_item TEI
 WHERE (TE.delete_flag IS NULL OR TE.delete_flag = 0)
   AND TE.status = 0
   AND now() > TE.end_datetime  -- ❌ TIME CONSTRAINT BLOCKS IMMEDIATE ENDING
   AND now() > (CASE WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime)
                     THEN TEI.end_datetime ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime) END)
   AND (SELECT COUNT(*) FROM t_exhibition_item TEI
        WHERE TEI.exhibition_no = TE.exhibition_no AND TEI.tenant_no = TE.tenant_no
          AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0) AND TEI.hummer_flag = 0) = 0
```

**The time constraints prevent immediate exhibition ending even when all items are complete.**

## Solution Plan

### Phase 1: Add Immediate Exhibition Ending Logic

#### 1.1 Create Helper Function for Exhibition Completion Check
**File**: `dll/function/auction-side/f_check_exhibition_completion.sql`

Create a reusable function to check if an exhibition should be ended immediately:
- Check if all items in exhibition have `hummer_flag != 0`
- Return exhibition completion status
- Handle multi-item exhibitions properly

#### 1.2 Modify f_regist_bid.sql - Add Immediate Exhibition Ending
**File**: `dll/function/auction-side/f_regist_bid.sql`
**Location**: After line 860 (immediate purchase logic)

Add logic to:
1. Check if current exhibition has all items completed after immediate purchase
2. If yes, immediately update `t_exhibition.status = 1`
3. Update `t_exhibition_summary.contract_count`
4. Ensure proper transaction handling

#### 1.3 Modify f_regist_bid_sealed.sql - Add Same Logic
**File**: `dll/function/auction-side/f_regist_bid_sealed.sql`
**Location**: After line 382 (immediate purchase logic)

Apply same immediate exhibition ending logic for sealed bid auctions.

### Phase 2: Enhance Batch Process for Immediate Purchase Cases

#### 2.1 Modify Exhibition Ending Batch Logic
**File**: `dll/function/batch/f_batch_exhibition_auction_end.sql`

Update the WHERE clause to handle immediate purchase cases:
```sql
AND (
  now() > TE.end_datetime  -- Normal time-based ending
  OR (  -- OR immediate ending when all items complete
    (SELECT COUNT(*) FROM t_exhibition_item TEI
     WHERE TEI.exhibition_no = TE.exhibition_no AND TEI.tenant_no = TE.tenant_no
       AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0) AND TEI.hummer_flag = 0) = 0
  )
)
```

### Phase 3: Testing Strategy

#### 3.1 Unit Testing
1. **Single Item Exhibition with Deal Price**
   - Bid below deal price → auction continues
   - Bid at deal price → immediate ending
   - Bid above deal price → immediate ending

2. **Multi-Item Exhibition with Deal Price**
   - One item reaches deal price, others active → auction continues
   - All items reach deal price → immediate ending
   - Mixed completion states → proper handling

3. **Edge Cases**
   - Concurrent bids on multiple items
   - Deal price exactly equal to bid
   - Exhibition with no deal price set

#### 3.2 Integration Testing
1. **End-to-End Flow**
   - Lambda → f_regist_bid_combine → f_regist_bid → Exhibition ending
   - Verify WebSocket notifications
   - Verify email notifications (if enabled)

2. **Batch Process Compatibility**
   - Ensure batch processes still work for time-based endings
   - Verify no duplicate exhibition ending
   - Test batch performance with immediate-ended exhibitions

### Phase 4: Implementation Order

#### Step 1: Create Helper Function
```sql
-- f_check_exhibition_completion.sql
-- Returns: exhibition_no, should_end_immediately, total_items, completed_items
```

#### Step 2: Modify f_regist_bid.sql
Add immediate exhibition ending logic after immediate purchase update.

#### Step 3: Modify f_regist_bid_sealed.sql
Apply same logic for sealed bid auctions.

#### Step 4: Update Batch Process
Modify f_batch_exhibition_auction_end.sql to handle immediate completion cases.

#### Step 5: Testing & Validation
- Test with provided scenario (deal_price: 40,000, bid: 43,333)
- Verify no "HUMMER_ERROR" on properly ended exhibitions
- Confirm exhibition status changes to "END" immediately

### Expected Behavior After Fix

#### Immediate Purchase Scenario:
1. **Bid Submission**: 43,333 yen (> 40,000 deal price)
2. **Item Update**: `hummer_flag = 1`, winning details updated
3. **Exhibition Check**: All items in exhibition completed?
4. **If Yes**: `t_exhibition.status = 1` (END), `t_exhibition_summary` updated
5. **Result**: Auction shows as "ENDED", no further bids accepted
6. **No "HUMMER_ERROR"**: Proper exhibition ending prevents error state

#### Multi-Item Exhibition:
- Only ends when ALL items reach completion (hummer_flag != 0)
- Partial completion keeps exhibition active
- Proper handling of mixed auction types within same exhibition

This solution addresses the core issue while maintaining compatibility with existing batch processes and ensuring proper auction lifecycle management.

## Detailed Implementation

### Step 1: Create Exhibition Completion Check Function

**File**: `dll/function/auction-side/f_check_exhibition_completion.sql`

```sql
CREATE OR REPLACE FUNCTION public.f_check_exhibition_completion(
    in_exhibition_no bigint,
    in_tenant_no bigint
)
RETURNS TABLE(
    exhibition_no bigint,
    should_end_immediately boolean,
    total_items integer,
    completed_items integer
)
LANGUAGE plpgsql
AS $BODY$
DECLARE
    item_count_total integer := 0;
    item_count_completed integer := 0;
BEGIN
    -- Count total active items in exhibition
    SELECT COUNT(*)
    INTO item_count_total
    FROM t_exhibition_item TEI
    WHERE TEI.exhibition_no = in_exhibition_no
      AND TEI.tenant_no = in_tenant_no
      AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0);

    -- Count completed items (hummer_flag != 0)
    SELECT COUNT(*)
    INTO item_count_completed
    FROM t_exhibition_item TEI
    WHERE TEI.exhibition_no = in_exhibition_no
      AND TEI.tenant_no = in_tenant_no
      AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
      AND TEI.hummer_flag != 0;

    RETURN QUERY
    SELECT
        in_exhibition_no,
        (item_count_total > 0 AND item_count_total = item_count_completed) as should_end_immediately,
        item_count_total,
        item_count_completed;
END;
$BODY$;
```

### Step 2: Modify f_regist_bid.sql - Add Immediate Exhibition Ending

**Location**: After line 860 in `dll/function/auction-side/f_regist_bid.sql`

```sql
-- Add after the immediate purchase logic (line 860)
------------------------------------------------------------------------------------
-- 即決後の開催回終了チェック: 全商品が確定した場合は開催回を即座に終了
------------------------------------------------------------------------------------
IF ext_deal_flag = 1 AND exhibition_rec.deal_bid_price IS NOT NULL AND exhibition_rec.deal_bid_price <= price THEN
  -- Check if exhibition should end immediately
  DECLARE
    completion_rec record;
  BEGIN
    SELECT * INTO completion_rec
    FROM f_check_exhibition_completion(exhibition_rec.exhibition_no, exhibition_rec.tenant_no);

    IF completion_rec.should_end_immediately THEN
      RAISE NOTICE '即決による開催回即座終了: exhibition_no=%, total_items=%, completed_items=%',
        exhibition_rec.exhibition_no, completion_rec.total_items, completion_rec.completed_items;

      -- End exhibition immediately
      UPDATE t_exhibition
      SET status = 1, update_datetime = now()
      WHERE exhibition_no = exhibition_rec.exhibition_no
        AND tenant_no = exhibition_rec.tenant_no
        AND status = 0;

      -- Update exhibition summary contract count
      UPDATE t_exhibition_summary
      SET contract_count = (
        SELECT count(DISTINCT lot_no)
        FROM t_exhibition_result TER
        WHERE TER.hummer_flag = 1
          AND TER.exhibition_no = exhibition_rec.exhibition_no
      ), update_datetime = now()
      WHERE exhibition_no = exhibition_rec.exhibition_no
        AND tenant_no = exhibition_rec.tenant_no;

      RAISE NOTICE '開催回即座終了完了: exhibition_no=%', exhibition_rec.exhibition_no;
    END IF;
  END;
END IF;
```

### Step 3: Modify f_regist_bid_sealed.sql - Add Same Logic

**Location**: After line 382 in `dll/function/auction-side/f_regist_bid_sealed.sql`

Apply the same immediate exhibition ending logic as above.

### Step 4: Update Batch Process for Compatibility

**File**: `dll/function/batch/f_batch_exhibition_auction_end.sql`
**Location**: Lines 29-33

```sql
-- Modify the WHERE clause to handle immediate completion cases
AND (
  now() > TE.end_datetime  -- Normal time-based ending
  OR (  -- OR immediate ending when all items complete (already handled by real-time logic)
    now() > (CASE
      WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime)
      THEN TEI.end_datetime
      ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
    END)
    AND (
      SELECT COUNT(*)
      FROM t_exhibition_item TEI_INNER
      WHERE TEI_INNER.exhibition_no = TE.exhibition_no
        AND TEI_INNER.tenant_no = TE.tenant_no
        AND (TEI_INNER.delete_flag IS NULL OR TEI_INNER.delete_flag = 0)
        AND TEI_INNER.hummer_flag = 0
    ) = 0
  )
)
```

### Step 5: Testing Commands

#### Test Case 1: Single Item Immediate Purchase
```sql
-- Setup test data
INSERT INTO t_exhibition_item (exhibition_item_no, tenant_no, exhibition_no, lot_no, quantity, deal_bid_price, hummer_flag, ...)
VALUES (50, 15, 1, 1, 1, 40000, 0, ...);

-- Test immediate purchase
SELECT * FROM "f_regist_bid_combine"(15, 'Buff Account 2', '[{"exhibitionItemNo": 50, "bidPrice": 43333, "bidQuantity": 1}]');

-- Verify results
SELECT exhibition_no, status FROM t_exhibition WHERE exhibition_no = 1;
SELECT exhibition_item_no, hummer_flag, current_price FROM t_exhibition_item WHERE exhibition_item_no = 50;
```

#### Test Case 2: Multi-Item Exhibition
```sql
-- Setup multiple items in same exhibition
INSERT INTO t_exhibition_item (exhibition_item_no, tenant_no, exhibition_no, lot_no, quantity, deal_bid_price, hummer_flag, ...)
VALUES
  (51, 15, 2, 1, 1, 40000, 0, ...),
  (52, 15, 2, 2, 1, 50000, 0, ...);

-- Test partial completion (should NOT end exhibition)
SELECT * FROM "f_regist_bid_combine"(15, 'Buff Account 2', '[{"exhibitionItemNo": 51, "bidPrice": 43333, "bidQuantity": 1}]');

-- Verify exhibition still active
SELECT exhibition_no, status FROM t_exhibition WHERE exhibition_no = 2; -- Should be 0

-- Test complete all items (should end exhibition)
SELECT * FROM "f_regist_bid_combine"(15, 'Buff Account 2', '[{"exhibitionItemNo": 52, "bidPrice": 53333, "bidQuantity": 1}]');

-- Verify exhibition ended
SELECT exhibition_no, status FROM t_exhibition WHERE exhibition_no = 2; -- Should be 1
```

### Step 6: Validation Checklist

- [ ] Exhibition ends immediately when all items reach deal price
- [ ] Exhibition remains active when only some items reach deal price
- [ ] No "HUMMER_ERROR" on properly ended exhibitions
- [ ] Batch processes continue to work for time-based endings
- [ ] WebSocket notifications work correctly
- [ ] Email notifications triggered appropriately
- [ ] Transaction integrity maintained
- [ ] Performance impact acceptable

This implementation ensures immediate auction termination while maintaining system integrity and compatibility with existing processes.
