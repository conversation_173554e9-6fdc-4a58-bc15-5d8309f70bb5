import {useChatStore} from '@/stores/chat'
import {useCognitoAuthStore} from '@/stores/cognitoAuth'
import {useMessageDialogStore} from '../stores/messag-dialog'
import useApi from './useApi'

/**
 * お問い合わせチャット関連の処理
 */
export default function useInquiryChat() {
  const auth = useCognitoAuthStore()
  const chatStore = useChatStore()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const dialog = useMessageDialogStore()

  const getInquiryChat = async (exhibition_item_no) => {

    const params = {
      exhibition_item_no,
    }
    try {
      if (auth.isAuthenticated) {
        // Call API
        const response = await apiExecute('private/get-inquiry-chat-by-auction', params)
        chatStore.setInquiryChat(response)
      } else {
        console.warn('❌️User is not authenticated, showing login required message')
        dialog.setShowMessage(translate('ERROR_CHAT_LOGIN_REQUIRED_MESSAGE'), {
          showOkButton: true,
        })
      }
    } catch (error) {
      // Revert optimistic updates on error
      console.log('Error in getInquiryChat:', error)
      parseHtmlResponseError(error)
    }
  }

  const registInquiryChat = async (exhibition_item_no, message) => {
    const params = {
      exhibition_item_no,
      message,
    }
    try {
      if (auth.isAuthenticated) {
        // Call API
        const response = await apiExecute('private/request-inquiry-chat-by-auction', params)
        return 'success';
      } else {
        console.warn('❌️User is not authenticated, showing login required message')
        dialog.setShowMessage(translate('ERROR_CHAT_LOGIN_REQUIRED_MESSAGE'), {
          showOkButton: true,
        })
        return '';
      }
    } catch (error) {
      // Revert optimistic updates on error
      const errMsg = parseHtmlResponseError(error)
      console.log('Error in registInquiryChat:', errMsg)
      dialog.setShowMessage(errMsg.message, {isErr: true})
      return '';
    }
  }

  return {getInquiryChat, registInquiryChat}
}
