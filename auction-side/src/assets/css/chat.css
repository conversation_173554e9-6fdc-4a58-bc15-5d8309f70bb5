@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *チャット
 * *********************************************************************** */
/* *========================================== */
#chat #main {
  padding: 0;
}

#chat-contact {
  background-color: #f7f7f7;
}
#chat-contact .container {
  width: 1280px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  position: relative;
}
@media screen and (max-width: 767px) {
  #chat-contact .container {
    width: 100%;
    max-width: 100%;
    margin: 0;
  }
}
#chat-contact .container .tab-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
}
#chat-contact .container .tab-wrap button {
  width: 50%;
  padding: 1.3rem 1rem;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .tab-wrap button {
    padding: 1rem 0.7rem;
  }
}
#chat-contact .container .tab-wrap button span {
  display: inline-block;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .tab-wrap button span {
    font-size: 1rem;
  }
}
#chat-contact .container .tab-wrap button.is-active {
  color: #fff;
  background-color: #427fae;
}
#chat-contact .container .tab-wrap button.is-active:hover {
  cursor: default;
  background-color: #427fae;
}
#chat-contact .container .tab-wrap button:hover {
  background-color: #c7e3c1;
  opacity: 1;
}
#chat-contact .container .chat-shipping {
  display: none;
}
#chat-contact .container .chat-item, #chat-contact .container .chat-shipping {
  position: relative;
  width: 100%;
  margin: 0;
  padding: 2rem 2rem;
  border: none;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item, #chat-contact .container .chat-shipping {
    margin: 0 auto;
    padding: 4vw 4vw 17vw;
  }
}
#chat-contact .container .chat-item h3, #chat-contact .container .chat-shipping h3 {
  margin: 1rem 0 1rem;
  font-size: 1.4rem;
  font-weight: 700;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item h3, #chat-contact .container .chat-shipping h3 {
    margin: 4vw 0;
    font-size: 3.8vw;
  }
}
#chat-contact .container .chat-item p.note, #chat-contact .container .chat-shipping p.note {
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item p.note, #chat-contact .container .chat-shipping p.note {
    font-size: 3vw;
  }
}
#chat-contact .container .chat-item .chat-head, #chat-contact .container .chat-shipping .chat-head {
  width: 1000px;
  max-width: 100%;
  margin: 2.5rem auto 2.5rem;
  padding: 1.5rem 3rem;
  background-color: #fff;
  border: none;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-head, #chat-contact .container .chat-shipping .chat-head {
    width: 100%;
    margin: 4vw 0;
    padding: 6vw 4vw 5vw;
  }
}
#chat-contact .container .chat-item .chat-head .label, #chat-contact .container .chat-shipping .chat-head .label {
  margin: 0 0 0.2rem;
  font-size: 0.9rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-head .label, #chat-contact .container .chat-shipping .chat-head .label {
    margin: 0 0 1vw;
    font-size: 3.5vw;
  }
}
#chat-contact .container .chat-item .chat-head .item-desc, #chat-contact .container .chat-shipping .chat-head .item-desc {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: flex-first;
      -ms-flex-align: flex-first;
          align-items: flex-first;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-head .item-desc, #chat-contact .container .chat-shipping .chat-head .item-desc {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#chat-contact .container .chat-item .chat-head .item-desc .item-name-b .name, #chat-contact .container .chat-shipping .chat-head .item-desc .item-name-b .name {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 400;
  line-height: 1.3;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-head .item-desc .item-name-b .name, #chat-contact .container .chat-shipping .chat-head .item-desc .item-name-b .name {
    margin: 0 0 3vw;
    font-size: 3.8vw;
  }
}
#chat-contact .container .chat-item .chat-head .item-desc .back-item-detail, #chat-contact .container .chat-shipping .chat-head .item-desc .back-item-detail {
  padding-left: 2rem;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-head .item-desc .back-item-detail, #chat-contact .container .chat-shipping .chat-head .item-desc .back-item-detail {
    margin: 2vw 0 2vw;
    padding: 0;
  }
}
#chat-contact .container .chat-item .chat-head .item-desc .back-item-detail button, #chat-contact .container .chat-shipping .chat-head .item-desc .back-item-detail button {
  width: 160px;
  height: 42px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0;
  background-color: #427fae;
  border: 1px solid #427fae;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-head .item-desc .back-item-detail button, #chat-contact .container .chat-shipping .chat-head .item-desc .back-item-detail button {
    width: 60vw;
    height: 11vw;
    margin: 0 auto;
    border-radius: 20vw;
  }
}
#chat-contact .container .chat-item .chat-head .item-desc .back-item-detail button span, #chat-contact .container .chat-shipping .chat-head .item-desc .back-item-detail button span {
  display: inline-block;
  color: #fff;
  font-size: 0.8rem;
  font-weight: 600;
  line-height: 1;
  white-space: nowrap;
  -webkit-transform: translateY(-1px);
          transform: translateY(-1px);
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-head .item-desc .back-item-detail button span, #chat-contact .container .chat-shipping .chat-head .item-desc .back-item-detail button span {
    font-size: 3.5vw;
  }
}
#chat-contact .container .chat-item .chat-head .item-desc .back-item-detail button:hover, #chat-contact .container .chat-shipping .chat-head .item-desc .back-item-detail button:hover {
  background-color: #fff;
}
#chat-contact .container .chat-item .chat-head .item-desc .back-item-detail button:hover span, #chat-contact .container .chat-shipping .chat-head .item-desc .back-item-detail button:hover span {
  color: #427fae;
}
#chat-contact .container .chat-item .chat-body, #chat-contact .container .chat-shipping .chat-body {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 1000px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 0 2rem;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body, #chat-contact .container .chat-shipping .chat-body {
    width: 100%;
    margin: 4vw auto;
    padding: 0;
  }
}
#chat-contact .container .chat-item .chat-body .chat-body-head, #chat-contact .container .chat-shipping .chat-body .chat-body-head {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0.8rem 1rem;
  background-color: #333;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body .chat-body-head, #chat-contact .container .chat-shipping .chat-body .chat-body-head {
    padding: 3vw 4vw;
  }
}
#chat-contact .container .chat-item .chat-body .chat-body-head .title, #chat-contact .container .chat-shipping .chat-body .chat-body-head .title {
  padding: 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body .chat-body-head .title, #chat-contact .container .chat-shipping .chat-body .chat-body-head .title {
    padding: 0;
  }
}
#chat-contact .container .chat-item .chat-body .chat-body-head .title p, #chat-contact .container .chat-shipping .chat-body .chat-body-head .title p {
  color: #fff;
  font-size: 1.2rem;
  font-weight: 600;
}
#chat-contact .container .chat-item .chat-body .chat-body-head .btn-wrap button, #chat-contact .container .chat-shipping .chat-body .chat-body-head .btn-wrap button {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: auto;
  height: 38px;
  padding: 0 1rem 0 1.9rem;
  color: #333;
  text-align: center;
  background-color: #efefef;
  border-radius: 4px;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body .chat-body-head .btn-wrap button, #chat-contact .container .chat-shipping .chat-body .chat-body-head .btn-wrap button {
    width: auto;
    height: 10vw;
    padding: 0 3vw 0 8.7vw;
  }
}
#chat-contact .container .chat-item .chat-body .chat-body-head .btn-wrap button span, #chat-contact .container .chat-shipping .chat-body .chat-body-head .btn-wrap button span {
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body .chat-body-head .btn-wrap button span, #chat-contact .container .chat-shipping .chat-body .chat-body-head .btn-wrap button span {
    font-size: 3.5vw;
  }
}
#chat-contact .container .chat-item .chat-body .chat-body-head .btn-wrap button:after, #chat-contact .container .chat-shipping .chat-body .chat-body-head .btn-wrap button:after {
  content: "";
  display: inline-block;
  background: url(../img/common/icn_refresh_bk.svg) no-repeat;
  background-size: 14px auto;
  background-position: center;
  width: 16px;
  height: 16px;
  position: absolute;
  top: calc(50% - 8px);
  left: 10px;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body .chat-body-head .btn-wrap button:after, #chat-contact .container .chat-shipping .chat-body .chat-body-head .btn-wrap button:after {
    background-size: 3.5vw;
    width: 4vw;
    height: 4vw;
    top: calc(50% - 2vw);
    left: 3.2vw;
  }
}
#chat-contact .container .chat-item .chat-body .chat-body-head .btn-wrap button:hover, #chat-contact .container .chat-shipping .chat-body .chat-body-head .btn-wrap button:hover {
  background-color: #ccc;
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
  max-height: 680px;
  padding: 3rem 3rem;
  overflow-y: scroll;
  scrollbar-width: 2px;
  background-color: #e1eaf0;
  border: 1px solid #c6d4dc;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body .chat-body-wrap, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap {
    max-height: 800px;
    padding: 7vw 4vw;
  }
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap::-webkit-scrollbar, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap::-webkit-scrollbar {
  width: 3px;
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap::-webkit-scrollbar-thumb, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 5px;
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail {
  margin-top: 1rem;
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail.question, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail.question {
  width: calc(100% - 100px);
  margin: 50px 0 0 auto;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail.question, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail.question {
    width: 80%;
    margin: 4vw 0 0 auto;
  }
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail.question:first-of-type, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail.question:first-of-type {
  margin-top: 0;
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail.answer, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail.answer {
  width: calc(100% - 200px);
  margin: 50px auto 0 0;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail.answer, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail.answer {
    width: 80%;
    margin: 4vw auto 0 0;
  }
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail:first-of-type, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail:first-of-type {
  margin-top: 0;
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-q, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-q {
  color: #427fae;
  font-weight: 600;
  font-size: 0.9rem;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-q, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-q {
    font-size: 3.5vw;
  }
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: auto;
  margin: 0.2rem 0 0;
  padding: 1.5rem 2rem;
  border-radius: 4px;
  font-size: 0.9rem;
  line-height: 1.6;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text {
    padding: 4vw;
    font-size: 3.2vw;
  }
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text.user, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text.user {
  background-color: #bfd4e5;
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text.seller, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text.seller {
  background-color: #fafafa;
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text.concealed, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text.concealed {
  color: #849db1;
  font-weight: 600;
  background-color: #bfd4e5;
}
#chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-a, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-a {
  color: #333;
  font-weight: 600;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-a, #chat-contact .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-a {
    font-size: 3.5vw;
  }
}
#chat-contact .container .chat-item .chat-foot, #chat-contact .container .chat-shipping .chat-foot {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 1000px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-foot, #chat-contact .container .chat-shipping .chat-foot {
    padding: 0;
  }
}
#chat-contact .container .chat-item .chat-foot .chat-form, #chat-contact .container .chat-shipping .chat-foot .chat-form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea {
    width: 100%;
    padding-right: 0;
  }
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea {
  -webkit-appearance: none;
  width: 100%;
  min-height: 110px;
  padding: 11px 12px;
  color: inherit;
  font-size: 0.9rem;
  resize: none;
  background-color: #fff;
  border: 1px solid #c6d4dc;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea {
    min-height: 50vw;
    padding: 2vw 3vw;
    font-size: 3.8vw;
  }
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea::-webkit-input-placeholder, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea::-webkit-input-placeholder {
  color: #ccc;
  font-size: 1rem;
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea::-moz-placeholder, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea::-moz-placeholder {
  color: #ccc;
  font-size: 1rem;
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea:-ms-input-placeholder, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea:-ms-input-placeholder {
  color: #ccc;
  font-size: 1rem;
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea::-ms-input-placeholder, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea::-ms-input-placeholder {
  color: #ccc;
  font-size: 1rem;
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea::placeholder, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea::placeholder {
  color: #ccc;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea::-webkit-input-placeholder, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea::-webkit-input-placeholder {
    font-size: 3.8vw;
  }
  #chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea::-moz-placeholder, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea::-moz-placeholder {
    font-size: 3.8vw;
  }
  #chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea:-ms-input-placeholder, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea:-ms-input-placeholder {
    font-size: 3.8vw;
  }
  #chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea::-ms-input-placeholder, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea::-ms-input-placeholder {
    font-size: 3.8vw;
  }
  #chat-contact .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea::placeholder, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea::placeholder {
    font-size: 3.8vw;
  }
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-note, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-note {
  width: 100%;
  padding: 0 0.2rem;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-foot .chat-form .chat-form-note, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-note {
    padding: 0 1vw;
  }
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-note ul li, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-note ul li {
  color: #e98181;
  font-size: 0.8rem;
  font-weight: 500;
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap {
  width: 100%;
  margin: 0 auto;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap {
    width: 100%;
    margin: 0;
  }
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 380px;
  height: 70px;
  margin: 2rem auto 3rem;
  color: #fff;
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.2;
  background-color: #427fae;
  border-radius: 40px;
  -webkit-transition: all ease 0.7s;
  transition: all ease 0.7s;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn {
    width: 100%;
    max-width: 100%;
    height: 16vw;
    margin: 4vw auto;
  }
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.clear, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.clear {
  width: 280px;
  height: 60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 auto 3rem;
  color: #427fae;
  font-size: 1rem;
  font-weight: 700;
  background-color: #fff;
  border: 1px solid #427fae;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.clear, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.clear {
    width: 100%;
    max-width: 100%;
    height: 14vw;
    margin: 4vw auto;
    font-size: 4vw;
  }
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn:hover, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn:hover {
  opacity: 0.8;
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.keep, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.keep {
  background: #364A81;
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.report, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.report {
  background: #E70012;
  color: #fff;
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.reset, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.reset {
  background: #666666;
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.submit, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.submit {
  background: #E04910;
}
#chat-contact .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.lt_gray, #chat-contact .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.lt_gray {
  background: #999999 !important;
}
/*# sourceMappingURL=chat.css.map */
