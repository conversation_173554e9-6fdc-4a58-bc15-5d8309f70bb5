<script setup>
  import {computed, defineProps, onMounted, onUnmounted, reactive, ref, watch} from 'vue'
  import {useLocale} from 'vuetify'
  import useBid from '../../../composables/bid'
  import {localeString2Number, priceLocaleString} from '../../../composables/common'
  import {priceMaxLength} from '../../../defined/const'
  import {usePrevRouteStore} from '../../../stores/prev-route'
  import {useSearchResultStore} from '../../../stores/search-results'
  import {eventBus, getAuctionStatusLabel, isLowerThanSM, navigateToPath} from '../../../utils'

  const props = defineProps(['item', 'exhibitionInfo', 'isAscendingAuction'])

  const {t} = useLocale()
  const store = useSearchResultStore()
  const {goToPath} = usePrevRouteStore()
  const {
    bidQuantity,
    bidPrice,
    bidTotalPrice,
    validateBidPrice,
    validateBidQuantity,
    validateBidInput,
    bidHandle,
    // clearBidInput
  } = useBid()

  const isAscendingAuction = ref(Number(props.item.auction_classification) === 1)
  const originalBidPrice = ref(
    store.productList.all.find(i => i.exhibition_item_no === props.item.exhibition_item_no)
      ?.bidPrice || ''
  )
  const isAscBidPriceDirty = reactive({})

  const canBid = computed(
    () => props.item.bid_status?.can_bid === true && !props.item.bid_status?.sold_out
  )
  const isBidPriceDirty = computed(
    () =>
      !!bidPrice.value && localeString2Number(bidPrice.value) !== props.item.bid_status?.bid_price
  )
  const isBidQuantityDirty = computed(
    () =>
      !!bidQuantity.value &&
      localeString2Number(bidQuantity.value) !== props.item.bid_status?.bid_quantity
  )
  const hasUserBid = computed(() => Number(props.item.attention_info.bid_count) >= 1)
  const exhibitionItemNo = computed(() => props.item.exhibition_item_no)
  const isCurrentItemDirty = computed(() => isAscBidPriceDirty[exhibitionItemNo.value] ?? false) //  tracks changes by computed

  const itemRef = computed(() => props.item)
  const statusLabel = getAuctionStatusLabel(itemRef, t)

  // onChange validate
  const isBidPriceError = computed(() => {
    if (!canBid.value) return false
    if (isBidPriceDirty.value || isBidQuantityDirty.value) {
      return validateBidPrice({
        currentBidPrice:
          props.item?.bid_status.current_price || props.item?.bid_status.lowest_bid_price,
        inputBidPrice: bidPrice.value,
        inputBidQuantity: bidQuantity.value,
        lowestBidPrice: props.item.bid_status?.lowest_bid_price,
        enteredBidPrice: props.item.bid_status?.bid_price,
        enteredBidQuantity: props.item.bid_status?.bid_quantity,
        isAscendingAuction: isAscendingAuction.value,
        hasUserBid: hasUserBid.value,
      })
    }
    return false
  })
  const isBidQuantityError = computed(() => {
    if (!canBid.value) return false
    if (isBidPriceDirty.value || isBidQuantityDirty.value) {
      return validateBidQuantity({
        inputBidPrice: bidPrice.value,
        inputBidQuantity: bidQuantity.value,
        lowestBidQuantity: props.item.bid_status?.lowest_bid_quantity,
        enteredBidPrice: props.item.bid_status?.bid_price,
        enteredBidQuantity: props.item.bid_status?.bid_quantity,
        maxQuantity: props.item.bid_status?.quantity,
      })
    }
    return false
  })
  const isBidButtonDisabled = computed(
    () =>
      !canBid.value ||
      isBidPriceError.value ||
      isBidQuantityError.value ||
      (!isBidPriceDirty.value && !isBidQuantityDirty.value)
  )

  const bidButtonClick = () => {
    bidHandle({
      exhibitionNo: props.exhibitionInfo.exhibition_no,
      exhibitionItemNo: props.item.exhibition_item_no,
      exhibitionName: props.exhibitionInfo.exhibition_name,
      lowestBidPrice: props.item?.bid_status?.lowest_bid_price,
      lowestBidQuantity: props.item?.bid_status?.lowest_bid_quantity,
      pitchWidth: props.item?.bid_status?.pitch_width,
      freeField: props.item.free_field,
      newBidPrice: bidPrice.value,
      newBidQuantity: bidQuantity.value,
      enteredBidPrice: props.item?.bid_status?.bid_price,
      enteredBidQuantity: props.item?.bid_status?.bid_quantity,
      maxQuantity: props.item?.bid_status?.quantity,
      isAscendingAuction: isAscendingAuction.value,
      currentBidPrice:
        props.item?.bid_status.current_price || props.item?.bid_status.lowest_bid_price,
      hasUserBid: hasUserBid.value,
      endDateTime: props.item?.bid_status?.end_datetime || props.item?.end_datetime,
    })
  }

  const initBidInput = () => {
    if (props.item.bid_status?.bid_price && props.item.bid_status.bid_quantity) {
      bidPrice.value = priceLocaleString(props.item.bid_status.bid_price)
      bidQuantity.value = priceLocaleString(props.item.bid_status.bid_quantity)
    } else {
      bidPrice.value = ''
      bidQuantity.value = ''
    }
  }

  const handleClearBidInput = () => {
    initBidInput()
    if (isAscendingAuction.value) {
      bidPrice.value = originalBidPrice.value
      isAscBidPriceDirty[exhibitionItemNo.value] = false
    }
  }

  const handleAfterBidSuccess = bidSuccessPrice => {
    isAscBidPriceDirty[exhibitionItemNo.value] = false
    originalBidPrice.value = bidSuccessPrice
  }

  // validate when blur the input field
  const onBidInputChanged = () => {
    store.productList?.all
      ?.filter(x => x.exhibition_item_no === props.item.exhibition_item_no)
      ?.forEach(x => {
        const validate = validateBidInput({
          inputBidPrice: bidPrice.value,
          inputBidQuantity: bidQuantity.value,
          currentBidPrice:
            props.item?.bid_status.current_price || props.item?.bid_status.lowest_bid_price,
          enteredBidPrice: props.item?.bid_status?.bid_price,
          enteredBidQuantity: props.item?.bid_status?.bid_quantity,
          lowestBidPrice: props.item?.bid_status?.lowest_bid_price,
          lowestBidQuantity: props.item?.bid_status?.lowest_bid_quantity,
          pitchWidth: props.item?.bid_status?.pitch_width,
          maxQuantity: props.item?.bid_status?.quantity,
          isAscendingAuction: isAscendingAuction.value,
          hasUserBid: hasUserBid.value,
        })
        x.bidInputError = {...validate}
        if (validate.bidPriceErr || validate.bidQuantityErr) {
          x.bidPrice = ''
          x.bidQuantity = ''
        } else {
          x.bidPrice = bidPrice.value
          x.bidQuantity = bidQuantity.value
        }
      })
  }

  const onBidPriceChange = () => {
    isAscBidPriceDirty[exhibitionItemNo.value] =
      localeString2Number(bidPrice.value) !== localeString2Number(originalBidPrice.value)
  }

  onMounted(() => {
    initBidInput()
    eventBus.on('onBidSuccess', (bidItemNo, bidSuccessPrice) => {
      if (props.item.exhibition_item_no === bidItemNo) {
        handleAfterBidSuccess(bidSuccessPrice)
      }
    })
  })

  onUnmounted(() => {
    eventBus.off('onBidSuccess', handleAfterBidSuccess)
  })

  // fetch entered(current) bid price and quantity when switch auction classifiction
  watch(
    () => store.selectedAucClassification,
    (oldVal, newVal) => {
      if (oldVal !== newVal) {
        initBidInput()
      }
    }
  )

  // 入札終了時点で入力していた価格がセットされている
  watch(
    () => canBid.value,
    val => {
      if (!val) {
        initBidInput()
      }
    }
  )
</script>
<template>
  <tr role="row">
    <!-- Product Name -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'itemname'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.productName') }}：</div>
      <a @click="navigateToPath(item.link, goToPath, $route)" class="cursor-pointer">
        {{ item.free_field?.product_name }}
      </a>
    </td>

    <!-- SIM -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'sim'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.sim') }}：</div>
      {{ item.free_field?.sim }}
    </td>

    <!-- Capacity -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'capacity'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.capacity') }}：</div>
      {{ item.free_field?.capacity }}
    </td>

    <!-- Color -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'color'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.color') }}：</div>
      {{ item.free_field?.color }}
    </td>

    <!-- Rank -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'grade'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.rank') }}：</div>
      {{ item.free_field?.rank }}
    </td>

    <!-- Quantity -->
    <td v-if="!isAscendingAuction" :class="isLowerThanSM ? 'sp-cell-flex' : 'quantity'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.quantity') }}：</div>
      {{ priceLocaleString(item.bid_status?.quantity) }}
    </td>

    <!-- Lowest Bid Quantity -->
    <td
      v-if="!isAscendingAuction"
      :class="isLowerThanSM ? 'sp-cell-flex' : 'lowestBid bg-highlight-status'"
    >
      <div v-show="isLowerThanSM">{{ t('productDetail.lowestBidQuantity') }}：</div>
      {{ priceLocaleString(item.bid_status?.lowest_bid_quantity) }}
    </td>

    <!-- Lowest Bid Price -->
    <td
      v-if="!isAscendingAuction"
      :class="isLowerThanSM ? 'sp-cell-flex' : 'price-min bg-highlight-status'"
    >
      <div v-show="isLowerThanSM">{{ t('productDetail.lowestBidPrice') }}：</div>
      <div>
        <span class="unit">$</span>
        <span>
          {{ priceLocaleString(item.bid_status?.lowest_bid_price) }}
        </span>
      </div>
    </td>
    <td
      v-if="!isAscendingAuction"
      :class="{
        'my-bid-quantity': true,
        error: isBidQuantityError,
        'bg-highlight-bid': !isLowerThanSM,
      }"
    >
      <div class="bidp-input-field">
        <v-tooltip>
          <template v-slot:activator="{props}">
            <span v-bind="props" v-if="isBidQuantityError" class="txt-error-w">{{
              t('common.inputError')
            }}</span>
          </template>
          <span>{{ isBidQuantityError }}</span>
        </v-tooltip>
        <p class="bidp-label">
          {{ t('bulkBid.inputBidQuantity') }}
          <span class="bid-quantity">
            {{ priceLocaleString(item.bid_status?.bid_quantity) }}
          </span>
        </p>
        <input
          type="text"
          data-id="price-bid"
          value=""
          :class="{
            'price-bid': true,
            'bg-white': canBid,
            'bg-surface-light': !canBid,
            'text-error': isBidQuantityDirty,
          }"
          :disabled="!canBid"
          placeholder="10"
          v-model="bidQuantity"
          @input="bidQuantity = bidQuantity?.replace(/[^0-9]/g, '')?.slice(0, priceMaxLength)"
          @blur="
            () => {
              bidQuantity = priceLocaleString(bidQuantity)
            }
          "
          @change="onBidInputChanged"
        />
      </div>
    </td>
    <td
      v-if="!isAscendingAuction"
      :class="{
        'my-bid-price': true,
        error: isBidPriceError,
        'bg-highlight-bid': !isLowerThanSM,
      }"
    >
      <div class="bidp-wrap">
        <div class="txt-wrap">
          <v-tooltip>
            <template v-slot:activator="{props}">
              <span
                v-bind="props"
                v-if="isBidPriceError && !isBidButtonDisabled"
                class="txt-error-w pl0"
                >{{ t('common.inputError') }}</span
              >
            </template>
            <span>{{ isBidPriceError }}</span>
          </v-tooltip>
          <p class="bidp-label">
            {{ t('bulkBid.inputBidPrice') }}
            <span class="unit">{{ t('productDetail.currency') }}</span>
            <span class="bid-price">{{ priceLocaleString(item.bid_status?.bid_price) }}</span>
          </p>
        </div>
        <div class="bidp-input-field">
          <span class="unit">{{ t('productDetail.currency') }}</span>
          <input
            type="text"
            data-id="price-bid"
            value=""
            :class="{
              'price-bid': true,
              'bg-white': canBid,
              'bg-surface-light': !canBid,
              'text-error': isBidPriceDirty,
            }"
            :disabled="!canBid"
            placeholder="10"
            v-model="bidPrice"
            @input="bidPrice = bidPrice?.replace(/[^0-9]/g, '')?.slice(0, priceMaxLength)"
            @blur="
              () => {
                bidPrice = priceLocaleString(bidPrice)
              }
            "
            @change="onBidInputChanged"
          />
        </div>
      </div>
    </td>
    <td v-if="!isAscendingAuction" class="subtotal" :class="{'bg-highlight-bid': !isLowerThanSM}">
      <div class="bidp-wrap">
        <p class="bidp-label">{{ t('favorite.subTotalBidPrice') }}</p>
        <div class="bidp-input-field">
          <span class="unit">{{ t('productDetail.currency') }}</span>
          <div class="price-subtotal flex-grow-1">{{ bidTotalPrice }}</div>
        </div>
      </div>
    </td>

    <!-- 現在価格 -->
    <td
      v-if="isAscendingAuction"
      :class="isLowerThanSM ? 'sp-cell-flex' : 'current-price bg-highlight-status'"
    >
      <div v-show="isLowerThanSM">{{ t('productDetail.info.currentPrice') }}：</div>
      <span class="current-price sp-text-red">
        {{ `${t('productDetail.currency')}${priceLocaleString(item.bid_status?.current_price)}` }}
      </span>
    </td>
    <!-- ステータス -->
    <td
      v-if="isAscendingAuction"
      :class="isLowerThanSM ? 'sp-cell-flex' : 'time-remaining bg-highlight-status'"
    >
      <div v-show="isLowerThanSM">{{ t('BID_STATUS') }}：</div>
      <div>
        <span class="time">
          {{ statusLabel }}
        </span>
        <span v-if="item.bid_status?.is_top_member" class="highest">
          {{ t('HIGHEST_BIDDER') }}
        </span>
      </div>
    </td>
    <!-- 入札価格 -->
    <td
      v-if="isAscendingAuction"
      class="my-bid-price error"
      :class="{'bg-highlight-bid': !isLowerThanSM}"
    >
      <div class="bidp-wrap">
        <v-tooltip bottom>
          <template v-slot:activator="{props}">
            <span
              v-bind="props"
              v-if="isBidPriceError && isCurrentItemDirty"
              class="txt-error-w pl0"
              >{{ t('common.inputError') }}</span
            >
          </template>
          <span>{{ isBidPriceError }}</span>
        </v-tooltip>
        <p class="bidp-label">
          {{ t('RECORDED_BID_PRICE') }}
          <span class="unit">{{ t('productDetail.currency') }}</span>
          <span class="bid-price">{{ priceLocaleString(item.bid_status?.bid_price) }}</span>
        </p>
        <div class="bidp-input-field">
          <span class="unit">
            {{ t('productDetail.currency') }}
          </span>
          <input
            type="text"
            data-id="price-bid"
            :class="{
              'price-bid': true,
              'bg-white': canBid,
              'bg-surface-light': !canBid,
              'text-error': isCurrentItemDirty,
            }"
            :disabled="!canBid"
            placeholder="10"
            v-model="bidPrice"
            @input="
              (bidPrice = bidPrice?.replace(/[^0-9]/g, '')?.slice(0, priceMaxLength)),
                onBidPriceChange()
            "
            @blur="
              () => {
                bidPrice = priceLocaleString(bidPrice)
              }
            "
            @change="onBidInputChanged"
          />
        </div>
      </div>
    </td>
    <!-- 入札(共通)  -->
    <td class="bidding-wrap" :class="{'bg-highlight-bid': !isLowerThanSM}">
      <button class="clear" @click="handleClearBidInput" :class="!canBid && 'disabled-btt'">
        {{ t('favorite.clearPriceInput') }}
      </button>
      <button
        :class="{
          bid: true,
          'opacity-50': isAscendingAuction
            ? !isCurrentItemDirty || isBidPriceError !== ''
            : isBidButtonDisabled,
        }"
        @click="bidButtonClick"
        :disabled="
          isAscendingAuction ? !isCurrentItemDirty || isBidPriceError !== '' : isBidButtonDisabled
        "
      >
        {{ t('favorite.reBidButton') }}
      </button>
    </td>
  </tr>
</template>

<style lang="css" scoped>
  .sp-cell-flex {
    display: flex !important;
    justify-content: space-between;
  }
  .disabled-btt {
    text-decoration: none !important;
    cursor: default;
  }

  #list-auction
    .container
    .auction-conteiner
    .auction-contents
    .list-item-table
    table
    tbody
    tr
    td.current-price {
    white-space: nowrap;
    color: #e60012;
    font-size: 1rem;
    font-weight: 600;
  }
  #list-auction
    .container
    .auction-conteiner
    .auction-contents
    .list-item-table
    table
    tbody
    tr
    td.current-price
    span {
    font-weight: 600;
  }
  .bg-highlight-status {
    background-color: #faf1f2;
  }

  .bg-highlight-bid {
    background-color: #f9e2e4;
  }
  /* Small devices (landscape phones, less than 768px) */
  @media screen and (max-width: 767.98px) {
    .time {
      font-size: 3vw;
      font-weight: 600;
      margin: 0 1vw;
    }
    .highest {
      display: inline-block !important;
      color: #ff0000;
      font-weight: 500;
      font-size: 3vw;
    }
    .item-flex-center {
      font-weight: 500;
    }
  }

  /* Small devices (landscape phones, less than 768px) */
  @media screen and (max-width: 767.98px) {
    .sp-text-red {
      color: #e60012;
      font-weight: 600;
    }
    #list-auction
      .container
      .auction-conteiner
      .auction-contents
      .list-item-table
      table.mypage-list
      tbody
      tr
      .bidding-wrap
      button.bid {
      padding: 0 !important;
    }
  }
</style>
