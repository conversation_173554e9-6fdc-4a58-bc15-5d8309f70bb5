<script setup>
  import VueCountdown from '@chenfengyuan/vue-countdown'
  import {computed} from 'vue'
  import {useLocale} from 'vuetify'

  const props = defineProps(['remainingSeconds'])
  const emit = defineEmits(['countdown-ended'])
  const {t: translate, current} = useLocale()

  console.log('remainingSeconds: ', props.remainingSeconds)
  const remainingTime = computed(() => {
    return (props.remainingSeconds || 0) * 1000
  })

  const onCountdownEnd = () => {
    emit('countdown-ended')
  }
</script>

<template>
  <VueCountdown
    :time="remainingTime"
    @end="onCountdownEnd"
    v-slot="{days, hours, minutes, seconds}"
  >
    <span class="countdown-text">
      残り
      <span v-if="days > 0">{{ days }}日</span>
      <span v-if="hours > 0">{{ hours }}時間</span>
      <span v-if="minutes > 0">{{ minutes }}分</span>
      {{ seconds }}秒
    </span>
  </VueCountdown>
</template>

<style scoped lang="scss">
  .countdown-text {
    color: #ff0000;
    font-weight: bold;
    font-size: 14px;
    display: inline;
  }
</style>
