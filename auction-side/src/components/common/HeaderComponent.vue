<script setup>
  import {PATH_NAME} from '@/defined/const'
  import {useCognitoAuthStore} from '@/stores/cognitoAuth.ts'
  import useChangeLanguage, {useLanguageStore} from '@/stores/language'
  import {useTenantSettingsStore} from '@/stores/tenantSettings'
  import {computed, ref} from 'vue'
  import {RouterLink, useRouter} from 'vue-router'
  import {useLocale} from 'vuetify'

  const auth = useCognitoAuthStore()
  const tenantSettings = useTenantSettingsStore()
  const router = useRouter()
  const languageStore = useLanguageStore()
  const {changeLanguage} = useChangeLanguage()
  const {t: translate, current: locale} = useLocale()
  const isChangingLanguage = ref(false)

  const logoText = computed(() => {
    return tenantSettings.companyName || tenantSettings.tenantName
  })

  const handleLogout = () => {
    auth.logout()
  }

  const handleLanguageChange = async event => {
    const selectedLanguage = event.target.value
    if (selectedLanguage === languageStore.currentLanguage) return

    isChangingLanguage.value = true
    try {
      // Update Vuetify locale
      locale.value = selectedLanguage
      // Update store and API
      await changeLanguage(selectedLanguage)
    } catch (error) {
      // Rollback on error
      locale.value = languageStore.currentLanguage
      console.error('Failed to change language:', error)
    } finally {
      isChangingLanguage.value = false
    }
  }

  const handleSearch = () => {
    router.push(PATH_NAME.SEARCH_RESULTS)
  }
</script>

<template>
  <!-- Header -->
  <header>
    <!-- gNav PC/SP start -->
    <div class="wrap-header-elm">
      <div class="l-header-info-links">
        <ul class="l-header-info-item">
          <li class="language-swich">
            <div class="lang-wrap">
              <select
                id="locale-switcher"
                class="lang"
                :value="languageStore.currentLanguage"
                @change="handleLanguageChange"
                :disabled="isChangingLanguage"
              >
                <option value="ja" :selected="languageStore.currentLanguage === 'ja'">JP</option>
                <option value="en" :selected="languageStore.currentLanguage === 'en'">EN</option>
              </select>
            </div>
          </li>
        </ul>
      </div>
      <div class="main-nav-wrap">
        <div class="h-top">
          <p class="btnMenu only_sp"><span class="ham"></span></p>
          <h1 class="h-top-logo">
            <RouterLink class="logo" :to="PATH_NAME.TOP">
              <img src="@/assets/img/common/logo_cecauction.png" alt="CEC AUCTION" />
            </RouterLink>
          </h1>
          <div class="h-top-menu only_sp">
            <div class="lang-wrap">
              <select
                id="locale-switcher-sp"
                class="lang"
                :value="languageStore.currentLanguage"
                @change="handleLanguageChange"
                :disabled="isChangingLanguage"
              >
                <option value="ja" :selected="languageStore.currentLanguage === 'ja'">JP</option>
                <option value="en" :selected="languageStore.currentLanguage === 'en'">EN</option>
              </select>
            </div>
          </div>
        </div>
        <div class="nav-elm">
          <div class="search-elm only_pc">
            <div class="search-category">
              <li>
                <a href="#" class="nav-label">{{ translate('HEADER_NAV_PRODUCT_CATEGORY') }}</a>
                <div class="menu-list">
                  <p class="arrow-box"></p>
                  <div class="panel-wrap">
                    <div class="category-box">
                      <div class="category-all">
                        <p>
                          <a>{{ translate('HEADER_NAV_ALL_CATEGORIES') }}</a>
                        </p>
                        <p>
                          <a>{{ translate('ASCENDING_AUCTION') }}</a>
                        </p>
                        <p>
                          <a>{{ translate('SEALED_AUCTION') }}</a>
                        </p>
                      </div>
                    </div>
                    <div class="category-box">
                      <div class="category-top">
                        <p>
                          <a>{{ translate('HEADER_NAV_CATEGORY_1') }}</a>
                        </p>
                      </div>
                      <div class="category-secondary">
                        <ul class="list-secondary">
                          <li><a href="./list/store-fixture/">かばん・バッグ</a></li>
                          <li><a href="./list/store-fixture/">財布</a></li>
                          <li><a href="./list/store-fixture/">靴</a></li>
                          <li><a href="./list/store-fixture/">サングラス</a></li>
                          <li><a href="./list/store-fixture/">アクセサリー</a></li>
                          <li><a href="./list/store-fixture/">ジャケット・ウェア</a></li>
                          <li><a href="./list/store-fixture/">帽子</a></li>
                          <li><a href="./list/store-fixture/">メンズライン</a></li>
                        </ul>
                      </div>
                    </div>
                    <div class="category-box">
                      <div class="category-top">
                        <p>
                          <a>{{ translate('HEADER_NAV_CATEGORY_2') }}</a>
                        </p>
                      </div>
                      <div class="category-secondary">
                        <ul class="list-secondary">
                          <li><a href="./list/store-fixture/">LOUIS VUITTON</a></li>
                          <li><a href="./list/store-fixture/">CHANEL</a></li>
                          <li><a href="./list/store-fixture/">HERMES</a></li>
                          <li><a href="./list/store-fixture/"> GUCCI</a></li>
                          <li><a href="./list/store-fixture/">PRADA</a></li>
                          <li><a href="./list/store-fixture/">BURBERRY</a></li>
                          <li><a href="./list/store-fixture/">FENDI</a></li>
                          <li><a href="./list/store-fixture/">CELINE</a></li>
                          <li><a href="./list/store-fixture/">Christian Dior</a></li>
                          <li><a href="./list/store-fixture/">ETRO</a></li>
                        </ul>
                      </div>
                    </div>
                    <div class="category-box">
                      <div class="category-top">
                        <p>
                          <a>{{ translate('HEADER_NAV_CATEGORY_3') }}</a>
                        </p>
                      </div>
                      <div class="category-secondary">
                        <ul class="list-secondary">
                          <li><a href="./list/store-fixture/">ハンドバッグ・2way</a></li>
                          <li><a href="./list/store-fixture/">ショルダーバッグ</a></li>
                          <li><a href="./list/store-fixture/">トートバッグ</a></li>
                          <li><a href="./list/store-fixture/">メンズバッグ</a></li>
                          <li><a href="./list/store-fixture/">ポーチ</a></li>
                          <li><a href="./list/store-fixture/">エコバッグ</a></li>
                          <li><a href="./list/store-fixture/">セカンドバッグ</a></li>
                          <li><a href="./list/store-fixture/">バスケット・かご</a></li>
                          <li><a href="./list/store-fixture/">クラッチバッグ</a></li>
                          <li><a href="./list/store-fixture/">スーツケース</a></li>
                          <li><a href="./list/store-fixture/">バニティバッグ</a></li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </div>
            <div class="search-keyword">
              <input
                type="text"
                data-id="shop-search-keyword"
                value=""
                class="side-search-keyword search-keyword"
                :placeholder="translate('HEADER_NAV_SEARCH_PLACEHOLDER')"
              />
              <button @click="handleSearch">
                <img src="@/assets/img/common/icn_search_gray.svg" />
              </button>
            </div>
            <div class="info-menu">
              <li>
                <a href="#" class="nav-label">{{ translate('HEADER_NAV_SITE_ABOUT') }}</a>
                <div class="menu-list">
                  <p class="arrow-box"></p>
                  <ul>
                    <li>
                      <RouterLink :to="PATH_NAME.PROFILE">
                        {{ translate('HEADER_NAV_COMPANY_INFO') }}
                      </RouterLink>
                    </li>

                    <li>
                      <RouterLink :to="PATH_NAME.TOKUSHO">
                        {{ translate('HEADER_NAV_TOKUSHO') }}
                      </RouterLink>
                    </li>
                    <li>
                      <RouterLink :to="PATH_NAME.FAQ">
                        {{ translate('HEADER_NAV_FAQ') }}
                      </RouterLink>
                    </li>
                  </ul>
                </div>
              </li>
            </div>
          </div>
          <ul class="nav-btn only_pc">
            <li class="nav-mypage favorite" v-if="!auth.isAuthenticated">
              <RouterLink :to="PATH_NAME.FAVORITES">
                <img src="@/assets/img/common/icn_nav_member.svg" />
                <span>{{ translate('HEADER_NAV_LOGIN') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage favorite">
              <RouterLink :to="PATH_NAME.FAVORITES">
                <img src="@/assets/img/common/icn_header_favorite.svg" />
                <span>{{ translate('HEADER_NAV_FAVORITES') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage bid">
              <RouterLink :to="PATH_NAME.BIDS">
                <img src="@/assets/img/common/icn_bid.svg" class="bid" />
                <span>{{ translate('HEADER_NAV_BIDDING') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage bidded">
              <RouterLink :to="PATH_NAME.BID_HISTORY">
                <img src="@/assets/img/common/icn_bidded.svg" class="bidded" />
                <span>{{ translate('HEADER_NAV_SUCCESSFUL_BID_HISTORY') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage registration" v-if="!auth.isAuthenticated">
              <RouterLink :to="PATH_NAME.COGNITO_REGISTER">
                <img src="@/assets/img/common/icn_registration.svg" class="bidded" />
                <span>{{ translate('HEADER_NAV_REGISTER') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage favorite" v-if="auth.isAuthenticated">
              <a @click="handleLogout" class="auth-button">
                <img src="@/assets/img/common/icn_nav_logout.svg" />
                <span>{{ translate('HEADER_NAV_LOGOUT') }}</span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <!-- gNav PC/SP end -->
    <!-- gNav SP start -->
    <div class="gNav only_sp">
      <nav>
        <ul class="only_sp">
          <li class="account">
            <button class="btn entry">{{ translate('HEADER_NAV_NEW_MEMBER_REGISTER') }}</button>
            <button class="btn login">{{ translate('HEADER_NAV_LOGIN') }}</button>
          </li>
          <li class="search">
            <input
              type="text"
              data-id="shop-search-keyword"
              value=""
              class="search-keyword"
              :placeholder="translate('HEADER_NAV_SEARCH_PLACEHOLDER')"
            />
            <button><img src="@/assets/img/common/icn_search.svg" /></button>
          </li>
          <li class="nav-black">
            <p>{{ translate('HEADER_NAV_PRODUCT_CATEGORY') }}</p>
            <ul>
              <li>
                <a href="/list_watch/">{{ translate('ASCENDING_AUCTION') }}</a>
              </li>
              <li>
                <a href="/list_bag/">{{ translate('SEALED_AUCTION') }}</a>
              </li>
              <li><a href="/list_bag/">ショルダーバッグ</a></li>
              <li><a href="/list_bag/">トートバッグ</a></li>
              <li><a href="/list_bag/">ポーチ</a></li>
              <li><a href="/list_bag/">セカンドバッグ</a></li>
              <li><a href="/list_bag/">ハンドバッグ</a></li>
              <li><a href="/list_bag/">財布</a></li>
              <li><a href="/list_bag/">小物</a></li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('HEADER_NAV_MEMBER_MENU') }}</p>
            <ul>
              <li>
                <a href="A">{{ translate('HEADER_NAV_MY_PAGE') }}</a>
              </li>
              <li>
                <a href="A">{{ translate('HEADER_NAV_LOGIN') }}</a>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('HEADER_NAV_SITE_ABOUT') }}</p>
            <ul>
              <li>
                <a href="A">{{ translate('HEADER_NAV_SHOPPING_GUIDE') }}</a>
              </li>
              <li>
                <a href="A">{{ translate('HEADER_NAV_MEMBER_SERVICES') }}</a>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('HEADER_NAV_GUIDANCE') }}</p>
            <ul>
              <li>
                <a href="/entryinfo.html">{{ translate('HEADER_NAV_CONTACT_US') }}</a>
              </li>
              <li>
                <a href="/login.html">{{ translate('HEADER_NAV_FAQ') }}</a>
              </li>
              <li>
                <a href="/mypage.html" target="_blank">{{
                  translate('HEADER_NAV_COMPANY_INFO')
                }}</a>
              </li>
              <li>
                <a href="./terms.html">{{ translate('HEADER_NAV_TERMS_OF_SERVICE') }}</a>
              </li>
            </ul>
          </li>
        </ul>
        <div class="line-logo">
          <div class="cont-wrap">
            <div class="pct">
              <RouterLink :to="PATH_NAME.TOP">
                <img src="@/assets/img/common/logo_cecauction.svg" />
              </RouterLink>
            </div>
            <div class="sns">
              <ul>
                <li>
                  <a href="A" class=""
                    ><img src="@/assets/img/common/icn_sns_facebook.svg" class="facebook"
                  /></a>
                </li>
                <li>
                  <a href="A" class=""><img src="@/assets/img/common/icn_sns_x.svg" class="x" /></a>
                </li>
                <li>
                  <a href="A" class=""
                    ><img src="@/assets/img/common/icn_sns_instagram.svg" class="instagram"
                  /></a>
                </li>
                <li>
                  <a href="A" class=""
                    ><img src="@/assets/img/common/icn_sns_youtube.svg" class="youtube"
                  /></a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="line-copyright">
          <div class="cont-wrap">
            <ul>
              <li>
                <a href="./">{{ translate('HEADER_NAV_SPECIFIED_COMMERCIAL_TRANSACTION_LAW') }}</a>
              </li>
              <li>
                <a href="./">{{ translate('HEADER_NAV_PRIVACY_POLICY') }}</a>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </div>
    <!-- gNav SP end -->
  </header>
</template>

<style lang="css" scoped>
  .nav-elm {
    align-items: center;
  }
  .info-menu {
    width: 120px !important;
  }

  .auth-button {
    cursor: pointer;
    transition: opacity 0.2s ease;
  }

  .auth-button:hover {
    opacity: 0.8;
  }

  button.auth-button {
    background: none;
    border: none;
    font: inherit;
    color: inherit;
    text-decoration: none;
  }

  a.auth-button {
    text-decoration: none;
  }
</style>
