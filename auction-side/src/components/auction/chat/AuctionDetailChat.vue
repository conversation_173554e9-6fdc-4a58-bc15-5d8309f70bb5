<script setup lang="ts">
  import BreadCrumb from '@/components/common/BreadCrumb.vue'
import type {ReactiveProductDetails, ResponseInquiryChat} from '@/composables/_type'
import useGetItemDetails from '@/composables/getItemDetails'
import useInquiryChat from '@/composables/inquiryChat'
import {useLanguageRefetch} from '@/composables/useLanguageRefetch.ts'
import {useTypedI18n} from '@/language/index.ts'
import {useChatStore} from '@/stores/chat'
import useGoBack from '@/stores/prev-route'
import {useSearchResultStore} from '@/stores/search-results'
import {computed, onBeforeMount, onMounted, ref} from 'vue'
import {useRoute} from 'vue-router'

  const {goBack} = useGoBack()
  const {inquiryChatData} = useChatStore()
  const {getInquiryChat, registInquiryChat} = useInquiryChat()
  const {productDetails} = useSearchResultStore()
  const {getItemDetailByExhItemNo} = useGetItemDetails()
  const route = useRoute()
  const {t: translate} = useTypedI18n()
  const {refetchOnLanguageChange} = useLanguageRefetch()

  const itemDetail = computed(() => productDetails as ReactiveProductDetails)
  const inquiryData = computed(() => inquiryChatData as ResponseInquiryChat)
  const exhibitionItemNo = route.params.exhibitionItemNo

  const isLoading = ref(true)
  const message = ref('')

  const initInquiryChat = async () => {
    isLoading.value = true

    if (exhibitionItemNo) {
      await getInquiryChat(exhibitionItemNo)
    }

    isLoading.value = false
  }

  const initItemDetail = async () => {
    isLoading.value = true
    await getItemDetailByExhItemNo(exhibitionItemNo, null)
  }

  const refetchItemData = async () => {
  // 言語切り替えに対応するため、商品詳細情報を再取得
    await initItemDetail()
  }


  onBeforeMount(async () => {
    await initInquiryChat()
  })

  onMounted(() => {
    refetchOnLanguageChange(refetchItemData)
  })

  const handleUpdate = async () => {
    await initInquiryChat()
  }

  const handleSubmit = async () => {
    isLoading.value = true
    try {
      const result = await registInquiryChat(exhibitionItemNo, message.value)
      await initInquiryChat()
      if (result === 'success') {
        message.value = ''
      }
    } catch (error) {
      console.log('error', error)
    } finally {
      isLoading.value = false
    }
  }

  const handleClear = () => {
    message.value = ''
  }
</script>

<template>
  <main id="main">
    <div id="pNav" class="bgLGray">
      <BreadCrumb customTitle="お問い合わせチャット" />
    </div>
    <h2 class="page-ttl mypage">
      <p class="ttl">お問い合わせチャット</p>
      <p class="sub">Chat</p>
    </h2>
    <section id="chat-contact">
      <div class="container">
        <!-- 商品についてのお問い合わせチャット Start -->
        <div class="chat-item">
          <!--<h3>商品についてのお問い合わせ</h3>-->
          <!--<p class="note">※以下の商品に関してのみ、お問い合わせを投稿いただけます。</p>-->
          <div class="chat-head">
            <p class="label">{{ translate('DETAIL_INFO_PRODUCT_NAME') }}</p>
            <div class="item-desc">
              <div class="item-name-b">
                <p class="name">
                  {{ itemDetail?.productName || itemDetail?.freeFields?.product_name || '' }}
                </p>
              </div>
              <div class="back-item-detail">
                <button @click="goBack">
                  <span>{{ translate('CHAT_BACK_ITEM') }}</span>
                </button>
              </div>
            </div>
          </div>
          <div class="chat-body">
            <div class="chat-body-head">
              <div class="title"><p>{{ translate('CHAT_CHAT_ROOM') }}</p></div>
              <div class="btn-wrap">
                <button class="refresh" @click="handleUpdate"><span>{{ translate('COMMON_UPDATE_AUCTION') }}</span></button>
              </div>
            </div>
            <div class="chat-body-wrap">
              <template v-for="(chat) in inquiryData" :key="chat.exhibition_message_no">
                <div class="chat-body-detail question" v-if="chat.update_category_id === '2' && chat.hidden_flag === 0">
                  <p class="chat-body-detail-q">{{ chat.member_name }}</p>
                  <p class="chat-body-detail-text user">{{ chat.message }}</p>
                </div>
                <div class="chat-body-detail answer" v-if="chat.update_category_id === '1'">
                  <p class="chat-body-detail-a">{{ translate('CHAT_SHOP') }}</p>
                  <p class="chat-body-detail-text seller">
                    {{ chat.message }}
                  </p>
                </div>
                <div class="chat-body-detail question" v-if="chat.update_category_id === '2' && chat.hidden_flag === 1">
                  <p class="chat-body-detail-q">{{ chat.member_name }}</p>
                  <p class="chat-body-detail-text concealed">
                    {{ translate('CHAT_HIDDEN_DESCRIPTION') }}
                  </p>
                </div>
              </template>
            </div>
          </div>
          <div class="chat-foot">
            <form class="chat-form" action="" @submit.prevent="handleSubmit">
              <div class="chat-form-textarea">
                <textarea name="" id="" :placeholder="translate('CHAT_INPUT_PLACEHOLDER')" v-model="message"></textarea>
              </div>
              <div class="chat-form-note">
                <ul>
                  <li>{{ translate('CHAT_ATTENTION_1') }}</li>
                  <li>{{ translate('CHAT_ATTENTION_2') }}</li>
                  <li>{{ translate('CHAT_ATTENTION_3') }}</li>
                </ul>
              </div>
              <div class="chat-form-btnWrap">
                <button class="chat-form-btn" type="submit">{{ translate('CHAT_SEND_BUTTON') }}</button>
                <button class="chat-form-btn clear" type="reset" @click="handleClear">{{ translate('CHAT_CLEAR_BUTTON') }}</button>
              </div>
            </form>
          </div>
        </div>
        <!-- 商品についてのお問い合わせチャット End -->
      </div>
    </section>
  </main>
</template>
