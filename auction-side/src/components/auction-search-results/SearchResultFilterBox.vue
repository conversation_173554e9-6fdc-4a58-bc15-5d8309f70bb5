<script setup lang="ts">
  import {useTypedI18n} from '@/language'

  // import {defineAsyncComponent} from 'vue'
  // import {useRoute} from 'vue-router'
  // import {PATH_NAME} from '@/defined/const'
  const {t: translate} = useTypedI18n()
</script>

<template>
  <section id="list-head">
    <div class="container">
      <div class="search-panel-wrap">
        <div class="head">{{ translate('FILTER_BOX_PRODUCT_SEARCH') }}</div>
        <div class="contents">
          <div class="keyword">
            <div class="keyword__label">{{ translate('FILTER_BOX_KEYWORD_FIELD') }}</div>
            <input
              type="text"
              data-id="shop-search-keyword"
              value=""
              class="search-keyword"
              :placeholder="translate('FILTER_BOX_SEARCH_PLACEHOLDER')"
            />
          </div>
          <div class="category-wrap">
            <div class="category-primary">
              <div class="model__label">{{ translate('TOP_SELECT_CATEGORY') }}</div>
              <div class="model__contents">
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">かばん・バッグ</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">ジャケット・アウター</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">シューズ</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">アクセサリー・時計</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">財布</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">サングラス</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">小物・インテリア用品</span>
                </label>
              </div>
            </div>
            <div class="category-secondary">
              <div class="model__label">絞り込み</div>
              <div class="model__contents">
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">LOUIS VUITTON</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">GUCCI</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">PRADA</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">CHANEL</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">HERMES</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">BURBERRY</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">FENDI</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">FENDI</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">BURBERRY</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">Christian Dior</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">GIVENCHY</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">FERRAGAMO</span>
                </label>
              </div>
            </div>
            <div class="category-tertiary">
              <div class="model__label">絞り込み</div>
              <div class="model__contents">
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">レディース</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">メンズ</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">キッズ</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">男女兼用</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">新品</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">限定モデル</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">キャラクター</span>
                </label>
                <label class="label-item">
                  <input id="checkbox" class="checkbox-model" type="checkbox" />
                  <span for="checkbox">コラボ</span>
                </label>
              </div>
            </div>
          </div>
          <div class="wrap-btn">
            <a class="clear">{{ translate('FILTER_BOX_CLEAR_CONDITIONS_ACTION') }}</a>
            <button class="btn search">{{ translate('FILTER_BOX_SEARCH_ACTION') }}</button>
          </div>
        </div>
      </div>
      <div class="conditions">
        <div class="conditions__label">
          <p class="ttl">{{ translate('FILTER_BOX_TITLE') }}</p>
          <p class="elm">
            <span>LOUIS VUITTON</span><span>トートバッグ</span><span>ショルダーバッグ</span>
          </p>
        </div>
        <div class="results__label">
          <p class="ttl">{{ translate('SEARCH_RESULT') }}</p>
          <p class="elm">
            281<span>{{ translate('FILTER_BOX_COUNT_UNIT') }}</span>
          </p>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="css" scoped>
  .search-keyword {
    background-color: #fff;
  }
  #main #list-head .conditions .results__label .ttl {
    width: initial !important;
    margin-right: 8px;
  }
</style>
