<script setup lang="ts">
  import type {AuctionClassification} from '@/composables/_type'
  import useFavorite from '@/composables/favorite'
  import {CLASSIFICATIONS, PATH_NAME} from '@/defined/const'
  import {useTypedI18n} from '@/language/index.ts'
  import router from '@/router'
  import {useLanguageStore} from '@/stores/language.js'
  import {usePrevRouteStore} from '@/stores/prev-route.js'
  import {useSearchResultStore} from '@/stores/search-results.js'
  import {computed, defineAsyncComponent, onMounted, onUnmounted, ref} from 'vue'
  import {RouterLink} from 'vue-router'
  import {useSlickSlider} from './useSlickSlider.js'
  import {topPageCategories, type TopPageCategoryItem} from './utils/category-list.ts'

  const PanelAuctionItem = defineAsyncComponent(
    () => import('@/components/common/auction/PanelAuctionItem.vue')
  )

  const store = useSearchResultStore()
  const {initSlider, cleanupSlider} = useSlickSlider()
  const {toggleFavorite} = useFavorite()
  const languageStore = useLanguageStore()

  const newItems = ref([])
  const recommendedItems = computed(() => {
    return store.productList.all.filter(item => item.is_recommending === true)
  })

  // TODO
  const newItemsFiltered = computed(() => {
    return store.productList.all
  })

  const {goToPath} = usePrevRouteStore()
  // Fetch new items and store separately
  const loadNewItems = async () => {
    try {
      // await fetchNewItems()
      newItems.value = [...store.productList.all]
    } catch (error) {
      console.error('Error loading new items:', error)
    } finally {
    }
  }

  // Event handlers
  const handleFavoriteToggle = async (exhibitionItemNo: string, currentFavorited: boolean) => {
    console.log('handleFavoriteToggle', exhibitionItemNo, currentFavorited)
    await toggleFavorite(exhibitionItemNo, currentFavorited)
    // Refresh both sections
    await loadNewItems()
    // await loadRecommendedItems()
    // Reinitialize slider after data refresh
    // await reinitializeSlider()
  }

  const handleClickItem = item => {
    console.log('Item clicked:', item)
    router.push(PATH_NAME.DETAILS)
  }

  const handleRefresh = async () => {
    console.log('Refresh handler called')
    await loadNewItems()
    // await loadRecommendedItems()
    // await reinitializeSlider()
  }

  const handleSearch = () => {
    router.push(PATH_NAME.SEARCH_RESULTS)
  }

  const handleCategoryClick = (category: TopPageCategoryItem) => {
    console.log('Category clicked:', category.name)
    router.push(category.routePath)
  }

  const handleAuctionTypeClick = (classificationType: AuctionClassification) => {
    const classificationValue =
      classificationType === 'ascending' ? CLASSIFICATIONS.ASCENDING : CLASSIFICATIONS.SEALED
    store.selectedAucClassification = classificationValue
  }

  const handleMoreCategory = () => {
    console.log('More category clicked')
    router.push(PATH_NAME.CATEGORY_LIST)
  }

  // Handlers for View Only Auction Item
  const handlers = {
    onFavoriteToggle: handleFavoriteToggle,
    onRefresh: handleRefresh,
    onItemClick: handleClickItem,
  }

  onMounted(async () => {
    await loadNewItems()
    await initSlider('.list-item-gallery')
  })

  onUnmounted(() => {
    cleanupSlider('.list-item-gallery')
  })

  const {t: translate} = useTypedI18n()
</script>

<template>
  <main id="main">
    <section id="heroes">
      <p class="catch">Auction Coming Soon!</p>
    </section>
    <section class="nav-list-mode-wrap">
      <div class="container">
        <RouterLink :to="PATH_NAME.SEARCH_RESULTS" @click="handleAuctionTypeClick('ascending')">
          <span>{{ translate('ASCENDING_AUCTION') }}</span>
        </RouterLink>
        <RouterLink :to="PATH_NAME.SEARCH_RESULTS" @click="handleAuctionTypeClick('sealed')">
          <span>{{ translate('SEALED_AUCTION') }}</span>
        </RouterLink>
      </div>
    </section>
    <section id="search">
      <div class="cont-wrap">
        <div class="search-keyword">
          <input
            type="text"
            data-id="shop-search-keyword"
            value=""
            class="side-search-keyword"
            :placeholder="translate('FILTER_BOX_SEARCH_PLACEHOLDER')"
          />
          <!-- カテゴリボタン start -->
          <div class="btn-category">
            <a href="javascript:void(0)"
              ><span>{{ translate('COMMON_MORE') }}</span></a
            >
          </div>
          <div class="filter-panel">
            <div class="panel-body">
              <div class="list-wrap">
                <ul class="list">
                  <li class="label pre">{{ translate('COMMON_MORE') }}</li>
                  <li class="label">LOUIS VUITTON</li>
                  <li class="label">CHANEL</li>
                </ul>
                <ul class="list">
                  <li class="label">HERMES</li>
                  <li class="label">GUCCI</li>
                  <li class="label">PRADA</li>
                </ul>
                <ul class="list">
                  <li class="label">BURBERRY</li>
                  <li class="label">FENDI</li>
                  <li class="label">CELINE</li>
                </ul>
                <ul class="list">
                  <li class="label">Christian Dior</li>
                  <li class="label">ETRO</li>
                  <li class="label">FENDI</li>
                </ul>
                <ul class="list">
                  <li class="label">GIVENCHY</li>
                  <li class="label">FURLA</li>
                  <li class="label">FERRAGAMO</li>
                </ul>
              </div>
              <p class="close-filter"><span></span></p>
            </div>
          </div>
          <!-- カテゴリボタン end -->
        </div>
        <button @click="handleSearch" class="btn-search">
          <img src="@/assets/img/common/icn_search.svg" />
        </button>
      </div>
    </section>

    <!-- カテゴリーから探す Start -->
    <section class="search-category">
      <p class="ttl">{{ translate('TOP_SEARCH_BY_CATEGORY') }}</p>
      <ul class="list-category">
        <li v-for="category in topPageCategories" :key="category.id">
          <a
            @click.prevent="handleCategoryClick(category)"
            :href="category.routePath"
            :title="category.description"
          >
            <figure>
              <img :src="category.imagePath" :alt="category.name" />
            </figure>
            <p>{{ category.name }}</p>
          </a>
        </li>
      </ul>
      <button class="btn more-category" @click="handleMoreCategory">
        <span>{{ translate('COMMON_MORE') }}</span>
      </button>
    </section>
    <!-- カテゴリーから探す End -->
    <!-- Information Start -->
    <section id="info">
      <div class="container-grid">
        <h2>
          NEWS<span>{{ translate('TOP_UPDATE_INFO') }}</span>
        </h2>
        <ul class="info-item">
          <li>
            <a href="A"
              ><span class="notice-day">2025/12/01</span>
              {{
                languageStore.currentLanguage === 'ja'
                  ? '【お詫び】一部商品の配送遅延について'
                  : 'Notice: Delay in Delivery of Certain Products'
              }}
            </a>
          </li>
          <li>
            <a href="A">
              <span class="notice-day">2025/12/01</span>
              {{
                languageStore.currentLanguage === 'ja'
                  ? '【新商品】〇〇シリーズ 販売開始のご案内'
                  : 'New Arrival: Launch of the XX Series'
              }}
            </a>
          </li>
          <li>
            <a href="A">
              <span class="notice-day">2025/12/01</span>
              {{
                languageStore.currentLanguage === 'ja'
                  ? '【重要】システムメンテナンスのお知らせ'
                  : 'Notice: System Maintenance Announcement'
              }}
            </a>
          </li>
        </ul>
        <div class="more-btn-wrap">
          <a href="A" class="btn">{{ translate('COMMON_MORE') }}</a>
        </div>
      </div>
    </section>
    <!-- Information End -->

    <!-- おすすめ Start -->
    <section id="list-recommend" class="list-item list-slider">
      <h2>
        <p class="ttl">{{ translate('TOP_RECOMMEND_PRODUCT') }}</p>
      </h2>
      <div class="container">
        <div v-if="recommendedItems.length > 0" :class="['item-list', 'panel']">
          <ul>
            <PanelAuctionItem
              v-for="item in recommendedItems"
              :key="item.exhibition_item_no"
              :item="item"
              view-mode="panel"
              :handlers="handlers"
              custom-classes=""
            />
          </ul>
        </div>
      </div>
    </section>
    <!-- おすすめ End -->

    <!-- Item List Start -->
    <section id="list-new" class="list-item">
      <h2>
        <p class="ttl">{{ translate('TOP_NEW_PRODUCTS') }}</p>
        <RouterLink :to="PATH_NAME.SEARCH_RESULTS" class="btn more">{{
          translate('COMMON_MORE')
        }}</RouterLink>
      </h2>

      <div class="container">
        <div v-if="newItemsFiltered.length > 0" :class="['item-list', 'panel']">
          <ul>
            <PanelAuctionItem
              v-for="item in newItemsFiltered"
              :key="item.exhibition_item_no"
              :item="item"
              view-mode="panel"
              :handlers="handlers"
              custom-classes=""
            />
          </ul>
        </div>
      </div>
    </section>
    <!-- Item List End -->
    <!-- about Start -->
    <section id="about">
      <div class="cont-wrap">
        <h2>
          <p class="ttl">{{ translate('TOP_ABOUT_TITLE') }}</p>
          <p class="s-ttl">{{ translate('TOP_ABOUT_SUBTITLE') }}</p>
        </h2>
        <div class="read">
          <p class="sb">
            {{ translate('TOP_ABOUT_DESCRIPTION_1') }}
          </p>
          <p class="st">
            {{ translate('TOP_ABOUT_DESCRIPTION_2') }}
          </p>
          <p class="sb">{{ translate('TOP_ABOUT_DESCRIPTION_3') }}</p>
          <p class="st">
            {{ translate('TOP_ABOUT_DESCRIPTION_4') }}
          </p>
        </div>
        <div class="btn-wrap">
          <a href="./other/about/" class="btn more">{{ translate('COMMON_MORE') }}</a>
        </div>
      </div>
    </section>
    <!-- about End -->
    <!-- signup Start -->
    <section id="signup">
      <div class="cont-wrap">
        <h2>
          <p class="s-ttl">{{ translate('TOP_SIGNUP_SUBTITLE') }}</p>
          <p class="ttl">{{ translate('TOP_SIGNUP_TITLE') }}</p>
        </h2>
        <div class="read">
          <p class="sb">{{ translate('TOP_SIGNUP_FREE_SHIPPING_TITLE') }}</p>
          <p class="st">
            {{ translate('TOP_SIGNUP_FREE_SHIPPING_DESC')
            }}<a href="/">{{ translate('TOP_SIGNUP_FREE_SHIPPING_LINK') }}</a
            >{{ translate('TOP_SIGNUP_FREE_SHIPPING_SUFFIX') }}
          </p>
          <p class="sb">{{ translate('TOP_SIGNUP_COUPON_TITLE') }}</p>
          <p class="st">
            {{ translate('TOP_SIGNUP_COUPON_DESC')
            }}<a href="./signin">{{ translate('TOP_SIGNUP_COUPON_LOGIN_LINK') }}</a
            >{{ translate('TOP_SIGNUP_COUPON_SUFFIX') }}
          </p>
        </div>
        <div class="btn-wrap">
          <a href="./other/member/" class="btn more">{{
            translate('TOP_SIGNUP_MEMBER_MORE_BUTTON')
          }}</a>
          <a href="./other/member/" class="btn signup">{{
            translate('TOP_SIGNUP_REGISTER_BUTTON')
          }}</a>
        </div>
      </div>
    </section>
    <!-- signup End -->
  </main>
</template>
