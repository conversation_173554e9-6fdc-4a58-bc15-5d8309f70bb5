import {fetchAuthSession as amplifyFetchAuthSession, signIn, signOut} from '@aws-amplify/auth'
import {Hub} from 'aws-amplify/utils'
import {jwtDecode} from 'jwt-decode'
import {defineStore} from 'pinia'
import {computed, ref} from 'vue'
import {useRouter} from 'vue-router'
import useApi from '../composables/useApi'
import i18n from '../language/index'
import type {TranslationKey} from '../language/translate'

// Function for use in Pinia stores
const t = (key: TranslationKey): string => {
  return i18n.global.t(key as string) as string
}

interface LoginHistoryMemberInfo {
  member_no: number
  user_no: number
  user_id: string
  member_id: string
  free_field: {
    email: string
    language: string
    memberName: string
  }
}

interface LoginHistoryResponse {
  success: boolean
  memberInfo: LoginHistoryMemberInfo
  message: string
}

interface CognitoUser {
  email: string
  memberNo: string
  userNo: string
  memberName: string
  languageCode: string
  tenantId: string
}

interface LoginResult {
  type: 'SUCCESS' | 'NEW_PASSWORD_REQUIRED' | 'EMAIL_VERIFICATION_REQUIRED' | 'ERROR'
  message?: string
}

interface LoginHistoryResult {
  success: boolean
  error?: string
}

// Translates Cognito error objects into localized messages
const errorToMessage = (error: any): string => {
  if (!error) return t('LOGIN_ERROR_UNKNOWN')

  if (error.message && error.message.includes('User is disabled.')) {
    return t('LOGIN_ERROR_ACCOUNT_DISABLED')
  }

  switch (error.name) {
    case 'UserNotConfirmedException':
      return t('LOGIN_ERROR_USER_NOT_CONFIRMED')
    case 'UserNotFoundException':
      return t('LOGIN_ERROR_INVALID_CREDENTIALS')
    case 'NotAuthorizedException':
      return t('LOGIN_ERROR_INVALID_CREDENTIALS')
    case 'UserAlreadyAuthenticatedException':
      return t('LOGIN_ERROR_USER_ALREADY_AUTHENTICATED')
    case 'LimitExceededException':
      return t('LOGIN_ERROR_LIMIT_EXCEEDED')
    case 'InvalidPasswordException':
      return t('LOGIN_ERROR_INVALID_PASSWORD')
    case 'TooManyRequestsException':
      return t('LOGIN_ERROR_TOO_MANY_REQUESTS')
    default:
      return error.message || t('LOGIN_ERROR_LOGIN_FAILED')
  }
}

// Extended JWT payload interface for Cognito tokens
interface CognitoJwtPayload {
  email: string
  'custom:member_no': string
  'custom:user_no': string
  'custom:member_name': string
  'custom:language_code': string
  'tenant-id': string
  [key: string]: any
}

export const useCognitoAuthStore = defineStore('cognitoAuth', () => {
  const router = useRouter()
  const {apiExecute} = useApi()

  const user = ref<CognitoUser | null>(null)
  const idToken = ref<string | null>(null)
  const accessToken = ref<string | null>(null)
  const memberInfo = ref<LoginHistoryMemberInfo | null>(null)
  const isAuthenticated = computed(() => !!user.value && !!idToken.value)

  function _setUserFromToken(token: string | null) {
    if (!token) {
      user.value = null
      idToken.value = null
      accessToken.value = null
      return
    }

    const decodedToken = jwtDecode<CognitoJwtPayload>(token)
    user.value = {
      email: decodedToken.email,
      memberNo: decodedToken['custom:member_no'],
      userNo: decodedToken['custom:user_no'],
      memberName: decodedToken['custom:member_name'],
      languageCode: decodedToken['custom:language_code'],
      tenantId: decodedToken['cognito:groups'][0].split(':')[1], // number, exg. 1
    }
    idToken.value = token
  }

  async function fetchAuthSession(forceRefresh: boolean = false): Promise<boolean> {
    try {
      const session = await amplifyFetchAuthSession({forceRefresh})
      const token = session.tokens?.idToken?.toString()
      if (!token) {
        throw new Error('No token found in session')
      }
      _setUserFromToken(token)
      accessToken.value = session.tokens?.accessToken?.toString() || null
      return true
    } catch (e) {
      user.value = null
      idToken.value = null
      accessToken.value = null
      memberInfo.value = null
      return false
    }
  }

  async function logLoginHistory(): Promise<LoginHistoryResult> {
    try {
      const result = await apiExecute<LoginHistoryResponse>(
        'private/login-history-logging',
        {
          languageCode: 'ja',
        },
        false, // not a file upload
        {
          authorization: `Bearer ${idToken.value}`,
        }
      )
      console.log('Login history logged successfully')

      // Store the memberInfo from the response
      if (result.success && result.memberInfo) {
        memberInfo.value = result.memberInfo
      }

      return {success: true}
    } catch (error: any) {
      console.warn('Login history logging error:', error)

      const errorMessage = error?.response?.data?.message || t('LOGIN_ERROR_LOGIN_HISTORY_FAILED')
      return {success: false, error: errorMessage}
    }
  }

  async function login(email: string, password: string): Promise<LoginResult> {
    try {
      const signInResponse = await signIn({
        username: email,
        password,
        options: {
          authFlowType: 'USER_PASSWORD_AUTH',
        },
      })

      if (signInResponse.isSignedIn) {
        await fetchAuthSession()

        // Log login history and check for backend errors (including tenant validation)
        const loginResult = await logLoginHistory()
        console.log(
          '%c 🇸🇻: useCognitoAuthStore -> loginResult ',
          'font-size:16px;background-color:#6cb43d;color:white;',
          loginResult
        )
        if (!loginResult.success && loginResult.error) {
          // Backend validation error - show error to user
          await logout()
          throw new Error(loginResult.error)
        }

        return {type: 'SUCCESS'}
      }

      const {signInStep} = signInResponse.nextStep
      switch (signInStep) {
        case 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED':
          return {
            type: 'NEW_PASSWORD_REQUIRED',
            message: t('LOGIN_ERROR_NEW_PASSWORD_REQUIRED'),
          }
        case 'CONFIRM_SIGN_UP':
          return {
            type: 'EMAIL_VERIFICATION_REQUIRED',
            message: t('LOGIN_ERROR_EMAIL_VERIFICATION_REQUIRED'),
          }
        case 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE':
          return {
            type: 'EMAIL_VERIFICATION_REQUIRED',
            message: t('LOGIN_ERROR_EMAIL_CODE_REQUIRED'),
          }
        default:
          return {
            type: 'ERROR',
            message: `${t('LOGIN_ERROR_UNSUPPORTED_STEP')} ${signInStep}`,
          }
      }
    } catch (error: any) {
      console.error('Login error:', error)
      throw new Error(errorToMessage(error))
    }
  }

  async function logout(): Promise<void> {
    try {
      await signOut()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      user.value = null
      idToken.value = null
      accessToken.value = null
      memberInfo.value = null
      router.push('/login')
    }
  }

  Hub.listen('auth', ({payload: {event}}) => {
    switch (event) {
      case 'signedIn':
        fetchAuthSession()
        break
      case 'signedOut':
      case 'tokenRefresh_failure':
        user.value = null
        idToken.value = null
        accessToken.value = null
        memberInfo.value = null
        break
    }
  })

  return {
    user,
    idToken,
    accessToken,
    // memberInfo, 今、使わないのでコメントアウトする
    isAuthenticated,
    fetchAuthSession,
    login,
    logout,
  }
})
