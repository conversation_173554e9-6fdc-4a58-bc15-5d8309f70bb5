import {defineStore} from 'pinia'
import {reactive} from 'vue'

export const useChatStore = defineStore('chat', () => {
  const inquiryChatData = reactive([])

  // 商品お問い合わせチャット（private/get-inquiry-chat-by-auction）のデータを格納
  const setInquiryChat = chat => {
    inquiryChatData.splice(0, inquiryChatData.length) // 既存データをクリア
    if (Array.isArray(chat) && chat.length > 0) {
      inquiryChatData.push(...chat)
    }
  }

  return {
    inquiryChatData,
    setInquiryChat,
  }
})
