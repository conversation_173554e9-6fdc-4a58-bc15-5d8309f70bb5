const Define = require(`${process.env.COMMON_LAYER_PATH}define`)
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool()
const ExcelProcess = require('./excel/excelProcess')
const ZipProcess = require('./zipProcess')

exports.handle = (e, ctx, cb) => {
  const Rules = require('./validation-rules')
  const params = Base.parseRequestBody(e.body)
  const isBatch = e.invokedFrom === 'BATCH' // バッチ判定追加
  if (isBatch) {
    console.log('THIS IS INVOKED FROM BATCH PROCESS')
  }
  pool.setMessage(Define.MESSAGE)

  ctx.callbackWaitsForEmptyEventLoop = false
  Promise.resolve()
    .then(() => (isBatch ? Promise.resolve() : Base.startRequest(e)))
    .then(() =>
      isBatch
        ? Promise.resolve()
        : Base.checkAccessIpAddress(
            pool,
            e.requestContext.identity.sourceIp,
            Base.extractTenantId(e)
          )
    )
    .then(() => {
      // Check if the product is not changed
      params.isSkipCheckEndDateTime = false
      params.isSkipCheckMaxExtendDateTime = false
      if (params.exhibition_no) {
        return pool
          .rlsQuery(Base.extractTenantId(e), Define.QUERY.GET_EXHIBITIONS, [
            params.exhibition_no,
            Base.extractTenantId(e),
            null,
            null,
            null,
            null,
            null,
            null,
          ])
          .then(existingExh => {
            if (existingExh && existingExh.length > 0) {
              if (params.end_datetime) {
                params.isSkipCheckEndDateTime =
                  Date.parse(params.end_datetime) -
                    Date.parse(existingExh[0]?.end_datetime) ===
                  0
              }
              if (params.max_extend_datetime) {
                params.isSkipCheckMaxExtendDateTime =
                  Date.parse(params.max_extend_datetime) -
                    Date.parse(existingExh[0]?.max_extend_datetime) ===
                  0
              }
            }
            return Promise.resolve()
          })
      }
      return Promise.resolve()
    })
    .then(() => {
      // 1年後の日付を取得
      const today = new Date(
        Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000
      )
      const maxDate = new Date(today)
      maxDate.setDate(today.getDate() + 364)
      maxDate.setHours(0, 0, 0, 0) // 時間をリセットして日付のみ取得

      // 延長設定が"あり"の場合の必須チェック
      Rules.extend_judge_minutes.REQUIRED_CHECK = params.extendFlag === '1'
      Rules.extend_minutes.REQUIRED_CHECK = params.extendFlag === '1'
      Rules.max_extend_datetime.REQUIRED_CHECK = params.extendFlag === '1'

      // あと少し表示が"あり"の場合の必須チェック
      Rules.more_little_judge_pitch.REQUIRED_CHECK =
        params.littleMoreDisplayFlag === '1'

      const now = new Date(
        Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000
      )
      // 下見開始時刻の整合性チェック
      if (params.previewStartDateTimeFlag) {
        if (
          params.preview_start_datetime &&
          !params.isSkipCheckPreviewStartDateTime
        ) {
          // 現在より過去ではないか
          params.compareWithPreviewStartAndNow =
            params.changePreviewStartDateTimeFlag
              ? 0
              : Date.parse(params.preview_start_datetime) - Date.parse(now)
          const compare =
            Date.parse(params.start_datetime) -
            Date.parse(params.preview_start_datetime)
          // 入札開始日より過去ではないか
          params.compareWithPreviewStartAndStart = compare
          // 1分以上の間隔が空いているか
          params.compareOneMinuteWithPreviewStartAndStart =
            compare === 0 ? 'error' : compare
          // 1年後以降の日付を選択していないか
          const previewStartDateTime = params.preview_start_datetime.split(' ')
          const previewStartDate = new Date(
            `${previewStartDateTime[0]}T00:00:00`
          )
          const previewStartDateDiff = maxDate - previewStartDate
          params.unselectOneLaterYearPreviewStartDate = previewStartDateDiff
        }
      } else {
        params.previewStartDateTimeVali = -1
      }
      // 入札期間の整合性チェック
      Rules.start_datetime.REQUIRED_CHECK = params.startDateTimeFlag
      if (params.startDateTimeFlag) {
        if (params.start_datetime && !params.isSkipCheckStartDateTime) {
          params.compareWithStartAndNow = params.exhibitionStartedFlag
            ? 0
            : Date.parse(params.start_datetime) - Date.parse(now)
          // 1年後以降の日付を選択していないか
          const startDateTime = params.start_datetime.split(' ')
          const startDate = new Date(`${startDateTime[0]}T00:00:00`)
          const startDateDiff = maxDate - startDate
          params.unselectOneLaterYearStartDate = startDateDiff
        }
      } else {
        params.startDateTimeVali = -1
      }
      Rules.end_datetime.REQUIRED_CHECK = params.endDateTimeFlag
      if (params.endDateTimeFlag) {
        if (params.end_datetime && !params.isSkipCheckEndDateTime) {
          params.compareWithEndAndNow = params.changeEndDateTimeFlag
            ? 0
            : params.max_extend_datetime
              ? 0
              : Date.parse(params.end_datetime) - Date.parse(now)
          // 1年後以降の日付を選択していないか
          const endDateTime = params.end_datetime.split(' ')
          const endDate = new Date(`${endDateTime[0]}T00:00:00`)
          const endDateDiff = maxDate - endDate
          params.unselectOneLaterYearEndDate = endDateDiff
        }
      } else {
        params.endDateTimeVali = -1
      }

      if (
        params.start_datetime &&
        params.end_datetime &&
        params.endDateTimeFlag &&
        params.startDateTimeFlag &&
        !params.isSkipCheckStartDateTime &&
        !params.isSkipCheckEndDateTime
      ) {
        const compare =
          Date.parse(params.end_datetime) - Date.parse(params.start_datetime)
        params.compareWithStartAndEnd = compare
        params.compareOneMinuteWithStartAndEnd =
          compare === 0 ? 'error' : compare
      }

      // 最大延長時刻の整合性チェック
      params.compareMaxExtendDate = ''
      if (params.extendDateTimeFlag) {
        if (
          params.max_extend_datetime &&
          (!params.isSkipCheckEndDateTime ||
            !params.isSkipCheckMaxExtendDateTime)
        ) {
          params.compareWithExtendAndNow =
            Date.parse(params.max_extend_datetime) - Date.parse(now)
          const compare =
            Date.parse(params.max_extend_datetime) -
            Date.parse(params.end_datetime)
          params.compareWithEndAndExtend = compare
          params.compareOneMinuteWithPreviewEndAndExtend =
            compare === 0 ? 'error' : compare
          // 1年後以降の日付を選択していないか
          const maxExtendDateTime = params.max_extend_datetime.split(' ')
          const maxExtendDate = new Date(`${maxExtendDateTime[0]}T00:00:00`)
          const maxExtendDateDiff = maxDate - maxExtendDate
          params.unselectOneLaterYearMaxExtendDate = maxExtendDateDiff
        }
      } else {
        params.extendDateTimeVali = -1
        Rules.max_extend_datetime.REQUIRED_CHECK = false
      }
      // 閲覧終了時間の整合性チェック
      params.comparePreviewEndDate = ''
      if (params.previewEndDateTimeFlag) {
        if (
          params.preview_end_datetime &&
          !params.isSkipCheckPreviewEndDateTime
        ) {
          params.compareWithPreviewEndAndNow =
            Date.parse(params.preview_end_datetime) - Date.parse(now)
          // 最大延長時刻ありの場合はそちらと比較する
          const compare =
            Date.parse(params.preview_end_datetime) -
            Date.parse(
              params.max_extend_datetime
                ? params.max_extend_datetime
                : params.end_datetime
            )
          Rules.comparePreviewEndDate.PATTERN_ERROR_MESSAGE =
            params.max_extend_datetime ? 'E000227' : 'E000221'
          Rules.comparePreviewEndDate2.PATTERN_ERROR_MESSAGE =
            params.max_extend_datetime ? 'E000228' : 'E000225'
          params.comparePreviewEndDate = compare
          params.comparePreviewEndDate2 = compare === 0 ? 'error' : compare

          // 1年後以降の日付を選択していないか
          const previewEndDateTime = params.preview_end_datetime.split(' ')
          const previewEndDate = new Date(`${previewEndDateTime[0]}T00:00:00`)
          const previewEndDateDiff = maxDate - previewEndDate
          params.unselectOneLaterYearPreviewEndDate = previewEndDateDiff
        }
      } else {
        params.previewEndDateTimeVali = -1
      }

      return Promise.resolve()
    })
    .then(() => {
      console.log('request params: ', params)
      return Validator.validation(params, Rules)
        .then(() => {
          return Promise.resolve()
        })
        .catch(error => {
          console.log('error: ', error)
          if (error.status === 400 && error.errors.exhibition_localized_json) {
            const exhibition_localized_json_error =
              error.errors.exhibition_localized_json
            const errorJson = error.errors
            const localizedErrorJSON = {}
            Object.keys(exhibition_localized_json_error).map(err => {
              Object.values(exhibition_localized_json_error[err]).map(ele => {
                const key = params.exhibition_localized_json[err].language_value
                localizedErrorJSON[key] = Common.format(ele, [
                  params.exhibition_localized_json[err].language_label,
                ])
                error.errors[key] = Common.format(ele, [
                  params.exhibition_localized_json[err].language_label,
                ])
              })
            })
            const errors = Object.assign(localizedErrorJSON, errorJson, {
              exhibition_localized_json: undefined,
            })
            return Promise.reject({
              status: 400,
              errors,
            })
          }
          return Promise.reject(error)
        })
    })
    .then(() => {
      console.log('CHECK EXCEL FILE')
      if (
        params.validation_excel &&
        params.excel_urls &&
        Object.keys(params.excel_urls).length > 0
      ) {
        const excelParam = {
          validation_mode: true,
          exhibition_no: params.exhibition_no,
          urls: params.excel_urls,
          end_datetime: params.end_datetime,
        }
        return ExcelProcess({
          params: excelParam,
          tenantNo: Base.extractTenantId(e),
          adminNo: Base.extractAdminNo(e),
          adminLanguageCode: Base.extractAdminLanguageCode(e),
        }).then(result => {
          console.log('ExcelProcess: ', result)
          return Promise.resolve()
        })
      }
      return Promise.resolve()
    })
    .then(() => {
      console.log('VALIDATE SUCCESSFUL!')
      // Validation successful
      if (params.validation_mode) {
        const response = {
          status: 200,
          message: '',
        }
        return Promise.reject(response)
      }
      return Promise.resolve()
    })
    .then(() => {
      console.log('UPDATE START/END DATETIME')
      if (params.exhibition_no) {
        // 入札開始・終了時間を変更した場合、延長になっていない出品情報の開始・終了時間も更新する
        const sqlParams = [
          params.exhibition_no,
          params.start_datetime,
          params.end_datetime,
          Base.extractAdminNo(e),
        ]

        return pool.rlsQuery(
          Base.extractTenantId(e),
          Define.QUERY.UPDATE_EXHIBITION_ITEM_DATETIME,
          sqlParams
        )
      }
      return Promise.resolve([])
    })
    .then(() => {
      console.log('UPDATE EXHIBITION DATA')
      console.log('Received tenantOptions:', params.tenantOptions)

      let exhibition_classification_info = {}
      if (params.auctionClassification === '1') {
        exhibition_classification_info = {
          auctionClassification: 1,
          openClassification: Number(params.tenantOptions.sankaseigen_seri),           // 公開区分 (1: 一般公開, 2: 特定の参加者)
          bidCountLimit: 0,
          bidLimit: Number(params.tenantOptions.bid_limit_seri),                       //入札額制限 TODO
          bidMinUseFlag: 1,
          bidShortFlag: 1,
          bidCommitMinUseFlag: Number(params.tenantOptions.minimum_price_seri),         // 最低落札価格 (0: なし、1: あり)
          bidCommitMinDisplayFlag: 1,
          littleMoreDisplayFlag: Number(params.littleMoreDisplayFlag),
          bidCancelFlag: Number(params.tenantOptions.bid_cancel_seri),                  //  入札取消可否（0: 不可, 1: 可能）
          dealFlag: Number(params.dealFlag),                              // 即決 (0: なし、1: あり)
          bidCommitClassification: Number(params.bidCommitClassification), // 落札価格決定区分 (1:ファーストプライス, 2: セカンドプライス)(bidOption.bid_success_price_fuin/bid_success_price_seri)
          bidderCommitClassification: Number(params.tenantOptions.winner_select_seri),  // 落札者決定 (0: なし、1: あり)
          extendFlag: Number(params.extendFlag),
          topReductionFlag: 1,
          showBidCountFlag: Number(params.showBidCountFlag),
        }
      } else if (params.auctionClassification === '2') {
        exhibition_classification_info = {
          auctionClassification: 2,
          openClassification: params.tenantOptions.sankaseigen_fuin,
          bidCountLimit: 0,
          bidLimit: params.tenantOptions.bid_limit_fuin,
          bidMinUseFlag: 1,
          bidShortFlag: 1,
          bidCommitMinUseFlag: params.tenantOptions.minimum_price_fuin,
          bidCommitMinDisplayFlag: 1,
          littleMoreDisplayFlag: Number(params.littleMoreDisplayFlag),
          bidCancelFlag: params.tenantOptions.bid_cancel_fuin,
          dealFlag: Number(params.dealFlag),
          bidCommitClassification: params.tenantOptions.bid_success_price_fuin,
          bidderCommitClassification: params.tenantOptions.winner_select_fuin,
          extendFlag: params.tenantOptions.extension_fuin !== null ? params.tenantOptions.extension_fuin : 0,
          topReductionFlag: 0,
          showBidCountFlag: Number(params.showBidCountFlag),
        }
      }

      console.log('Built exhibition_classification_info:', exhibition_classification_info)

      // 入札会名再構築
      const json = params.exhibition_localized_json
      params.exhibition_localized_json = {}
      for (const x of json) {
        params.exhibition_localized_json[x.language_value] = {
          exhibition_name: x.exhibition_name,
          language_label: x.language_label,
        }
      }

      const sqlParams = [
        params.exhibition_no,
        Base.extractTenantId(e),
        params.category ?? 0,
        params.pitch_option,
        0,
        params.exhibition_localized_json,
        params.preview_start_datetime,
        params.preview_end_datetime,
        params.start_datetime,
        params.end_datetime,
        params.max_extend_datetime,
        params.extend_judge_minutes,
        params.extend_minutes,
        params.pitch_width,
        params.more_little_judge_pitch,
        exhibition_classification_info,
        params.joinObject,
        Base.extractAdminNo(e),
      ]
      console.log('📦 log of UPSERT_EXHIBITIONS params : ', sqlParams)

      return pool
        .rlsQuery(
          Base.extractTenantId(e),
          Define.QUERY.UPSERT_EXHIBITIONS,
          sqlParams
        )
        .then(result => {
          console.log('result: ', result)

          const exh_result = result && result.length > 0 ? result[0] : null
          if (exh_result) {
            if (exh_result.ret_status === 200) {
              // Get exhibition no
              return Promise.resolve(exh_result.data)
            }
            // 同じ地区で同じ入札会名が存在している
            const msg = Define.MESSAGE[exh_result.message]
            // msg = exh_result.localize_error + msg
            const err = {
              status: 400,
              errors: {
                exhibition_name: msg,
              },
            }
            console.log('err: ', err)
            return Promise.reject(err)
          }
          const err = {
            status: 400,
            errors: {
              exh_result: Define.MESSAGE.E100407,
            },
          }
          return Promise.reject(err)
        })
    })
    .then((ret_exhibition_no) => {
      const sqlParams = [
        Base.extractTenantId(e),
        ret_exhibition_no,
        params.exhibitionMemberNos,
        Base.extractAdminNo(e)
      ];

      return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.REGIST_EXHIBITION_MEMBERS, sqlParams)
      .then(() => Promise.resolve(ret_exhibition_no))
    })
    .then(ret_exhibition_no => {
      console.log('UPDATE FROM EXCEL DATA')
      console.log('ret_exhibition_no: ', ret_exhibition_no)
      if (params.excel_urls && Object.keys(params.excel_urls).length > 0) {
        const excelParam = {
          validation_mode: false,
          exhibition_no: ret_exhibition_no,
          urls: params.excel_urls,
        }
        return ExcelProcess({
          params: excelParam,
          tenantNo: Base.extractTenantId(e),
          adminNo: Base.extractAdminNo(e),
          adminLanguageCode: Base.extractAdminLanguageCode(e),
        }).then(result1 => {
          console.log('ExcelProcess: ', result1)
          return Promise.resolve(result1)
        })
      }
      return Promise.resolve()
    })
    .then(result => {
      console.log('UPDATE IMAGES FROM ZIP FILE')
      if (params.zip_url) {
        const zipParam = {
          validation_mode: false,
          url: params.zip_url,
        }
        return ZipProcess(zipParam, e.authorizer).then(result1 => {
          console.log('ZipProcess: ', result1)
          return Promise.resolve(result1)
        })
      }
      return Promise.resolve()
    })
    .then(result => {
      if (isBatch) {
        Base.createSuccessResponse(cb, [])
      } else {
        Base.createSuccessResponse(cb, result)
      }
    })
    .catch(error => {
      console.log('error: ', error)
      return Base.createErrorResponse(cb, error)
    })
}
