const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.PGHOST)

exports.handle = (e, ctx, cb) => {
  const params = Common.parseRequestBody(e.body)
  console.log('👷‍♂️ log of params : ', params)
  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  console.log('🔒 log of authorizer : ', authorizer)

  const base = new Base(pool, params.languageCode)
  const validator = new Validator(base)

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => validator.validate(params))
    .then(() => {
      const sqlParams = [
        params.exhibition_item_no,
        authorizer.tenant_no,
      ]
      console.log('🔎 SQL params:', JSON.stringify(sqlParams, null, 2))
      const sql = `SELECT * FROM "f_get_inquiry_chat_by_auction"(${Common.sqlParamNumber(sqlParams.length)});`
      console.log('🗂 SQL query:', sql)

      return pool.rlsQuery(authorizer.tenant_no, sql, sqlParams)
    })
    .then(result => {
      console.log('☎️ log of query f_get_inquiry_chat_by_auction : ', result)
      return base.createSuccessResponse(cb, result)
    })
    .catch(error => base.createErrorResponse(cb, error))
}
