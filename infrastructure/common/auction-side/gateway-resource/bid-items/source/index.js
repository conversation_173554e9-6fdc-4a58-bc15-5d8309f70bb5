const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const pool = new PgPool()

const errorMapping = {
  BID_BANNED_ERROR: 'E100235',
  BID_CANCEL_ERROR: 'E100242',
  BID_COUNT_LIMIT_EXCEED_ERROR: 'E100243',
  BID_LIMIT_CURRENT_PRICE_EXCEED_ERROR: 'E100245',
  BID_LIMIT_EXCEED_ERROR: 'E100244',
  CREDIT_BALANCE_ERROR: 'E100247',
  CURRENCY_NOT_MATCH_ERROR: 'E100238',
  EXHIBITION_ITEM_FORMAT_ERROR: 'E000174',
  EXHIBITION_ITEM_NOT_FOUND_ERROR: 'E000177',
  EXHIBITION_ITEM_REQUIRED_ERROR: 'E000173',
  EXHIBITION_NOT_FOUND_ERROR: 'E100234',
  HUMMER_ERROR: 'E100236',
  LOWER_THAN_LOWEST_BID_ACCEPT_PRICE_ERROR: 'E100240',
  LOWER_THAN_LOWEST_BID_PRICE_ERROR: 'E100239',
  LOWER_THAN_LOWEST_BID_QUANTITY_ERROR: 'E100250',
  UPPER_THAN_QUANTITY_ERROR: 'E100253',
  MEMBER_NOT_ALLOW_BIDDING_ERROR: 'E100233',
  MIN_PITCH_ERROR: 'E100241',
  PERIOD_ERROR: 'E100237',
  PRICE_FORMAT_ERROR: 'E100232',
  PRICE_REQUIRED_ERROR: 'E100231',
  QUANTITY_REQUIRED_ERROR: 'E100251',
  QUANTITY_FORMAT_ERROR: 'E100252',
  PRICE_TOO_LOW_THAN_CURRENT_ERROR: 'E100245',
  PRICE_TOO_LOW_THAN_PREV_ERROR: 'E100246',
  SYSTEM_ERROR: 'E900001',
}

exports.handle = (e, ctx, cb) => {
  console.log(`event:`, e)
  const params = Common.parseRequestBody(e.body)
  const header = e.headers

  const authorizer = {
    user_no: Common.extractUserNo(e),
    member_name: Common.extractMemberName(e),
    member_no: Common.extractMemberNo(e),
    tenant_no: Common.extractTenantId(e),
  }
  console.log(`authorizer:`, authorizer)

  const base = new Base(pool, params.languageCode)
  pool.setMessage(base.define.message)

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(() => {
      console.log('params:', JSON.stringify(params, null, 2))
      return pool.rlsQuery(
        authorizer.tenant_no,
        'SELECT * FROM "f_regist_bid_combine"($1,$2,$3);',
        [
          authorizer.user_no,
          authorizer.member_name,
          JSON.stringify(params.bidItems),
        ]
      )
    })
    .then(data => {
      console.log(`data${JSON.stringify(data)}`)
      const result =
        data.length > 0 && Object.values(data[0]).length > 0
          ? Object.values(data[0])[0].result
          : {}

      // Check for bid validation errors
      const hasErrors = result.errorCount > 0 ||
        (result.bidList && result.bidList.some(bid => bid.errorMessage))

      if (hasErrors) {
        console.log(`Bid validation errors detected:`, JSON.stringify(result))
        // Map error codes to localized messages
        const errorBidList = result.bidList.map(bid => {
          if (
            bid.errorMessage &&
            errorMapping[bid.errorMessage] &&
            base.define.message[errorMapping[bid.errorMessage]]
          ) {
            bid.errorMessage = base.define.message[errorMapping[bid.errorMessage]]
          }
          return bid
        })
        const err = errorBidList[0].errorMessage

        // Throw error to be handled in catch block
        return Promise.reject({
          statusCode: 400,
          errorCode: 'BID_VALIDATION_ERROR',
          message: err || 'Bid validation failed',
          bidList: errorBidList,
          errorCount: result.errorCount
        })
      }

      // Only proceed with success flow if no errors
      return Promise.resolve()
        .then(() => {
          // TOP入れ替え時にTOPだった会員に送付される通知メール
          console.log(`TOP入れ替え時にTOPだった会員に送付される通知メール`)
          return Common.invokeLambdaEventType(
            process.env.SEND_BID_MAIL_LAMBDA_ARN,
            {
              bidResult: result,
              authorizer,
              languageCode: params.languageCode,
            },
            {maxRetries: 0}
          )
        })
        .then(() => {
          if (
            result.bidList.filter(bid => bid.auction_classification === '1')
              .length > 0
          ) {
            // invoke push-changed-items function to send channed item to client by websocket
            return Common.invokeLambdaEventType(
              process.env.PUSH_CHANGED_ITEMS_LAMBDA_ARN,
              {
                authorizer,
                bidResult: result,
              },
              {maxRetries: 0}
            )
          }
          return Promise.resolve()
        })
        .then(() => {
          if (result.statusCode === 200) {
            return base.createSuccessResponse(
              cb,
              {
                bidList: result.bidList.map(bid => {
                  if (
                    bid.errorMessage &&
                    errorMapping[bid.errorMessage] &&
                    base.define.message[errorMapping[bid.errorMessage]]
                  ) {
                    bid.errorMessage =
                      base.define.message[errorMapping[bid.errorMessage]]
                  }
                  return bid
                }),
              },
              false
            )
          }
          return Promise.reject(result)
        })
    })
    .catch(error => {
      console.log(`error${JSON.stringify(error)}`)
      if (error && error === 'MEMBER_NOT_ALLOW_BIDDING_ERROR') {
        params.bidItems.map(bid => {
          bid.exhibition_item_no = bid.exhibitionItemNo
          bid.errorMessage = base.define.message[errorMapping[error]]
          return bid
        })
        return base.createSuccessResponse(
          cb,
          {
            statusCode: 200,
            bidList: params.bidItems,
          },
          false
        )
      }
      return base.createErrorResponse(cb, error)
    })
}
